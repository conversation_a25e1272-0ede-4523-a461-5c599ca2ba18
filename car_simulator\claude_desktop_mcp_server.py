#!/usr/bin/env python3
"""
Claude Desktop MCP Server for Blender Control
This is a proper MCP server that can be configured in Claude Desktop
"""

import asyncio
import json
import sys
import socket
import subprocess
from pathlib import Path
from typing import Any, Dict, List, Optional

# MCP imports
try:
    from mcp.server import Server
    from mcp.server.models import InitializationOptions
    from mcp.server.stdio import stdio_server
    from mcp.types import (
        CallToolRequest,
        CallToolResult,
        ListToolsRequest,
        ListToolsResult,
        Tool,
        TextContent,
    )
except ImportError:
    print("Error: MCP library not installed. Install with: pip install mcp")
    sys.exit(1)

# Global variables
server = Server("blender-mcp-server")
BLENDER_HOST = "localhost"
BLENDER_PORT = 9876

class BlenderConnection:
    """Handle connection to Blender MCP server"""
    
    def __init__(self):
        self.socket = None
        self.connected = False
    
    def connect(self) -> bool:
        """Connect to Blender MCP server"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(5)  # 5 second timeout
            self.socket.connect((BLENDER_HOST, BLENDER_PORT))
            self.connected = True
            return True
        except Exception as e:
            self.connected = False
            return False
    
    def disconnect(self):
        """Disconnect from Blender"""
        if self.socket:
            self.socket.close()
            self.connected = False
    
    def send_command(self, command: str) -> Dict[str, Any]:
        """Send Python command to Blender"""
        if not self.connected:
            return {"status": "error", "message": "Not connected to Blender"}
        
        try:
            # Send command
            message = json.dumps({"command": command}) + "\n"
            self.socket.send(message.encode('utf-8'))
            
            # Receive response
            response = self.socket.recv(4096).decode('utf-8')
            return json.loads(response) if response else {"status": "error", "message": "No response"}
            
        except Exception as e:
            return {"status": "error", "message": str(e)}

# Global Blender connection
blender_conn = BlenderConnection()

@server.list_tools()
async def list_tools() -> ListToolsResult:
    """List available tools for Blender control"""
    return ListToolsResult(
        tools=[
            Tool(
                name="blender_connect",
                description="Connect to Blender MCP server",
                inputSchema={
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            ),
            Tool(
                name="blender_execute",
                description="Execute Python code in Blender",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "code": {
                            "type": "string",
                            "description": "Python code to execute in Blender"
                        }
                    },
                    "required": ["code"]
                }
            ),
            Tool(
                name="blender_load_infernus",
                description="Load the Infernus car model in Blender",
                inputSchema={
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            ),
            Tool(
                name="blender_setup_lighting",
                description="Setup professional studio lighting in Blender",
                inputSchema={
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            ),
            Tool(
                name="blender_create_animation",
                description="Create rotation animation for the car",
                inputSchema={
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            ),
            Tool(
                name="blender_render",
                description="Render the current scene",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "output_path": {
                            "type": "string",
                            "description": "Output path for rendered image (optional)"
                        }
                    },
                    "required": []
                }
            ),
            Tool(
                name="blender_status",
                description="Get status information from Blender",
                inputSchema={
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            ),
            Tool(
                name="blender_change_car_color",
                description="Change the color of the car",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "color": {
                            "type": "string",
                            "description": "Color name (red, blue, green, yellow, black, white) or RGB values"
                        }
                    },
                    "required": ["color"]
                }
            )
        ]
    )

@server.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> CallToolResult:
    """Handle tool calls"""
    
    if name == "blender_connect":
        success = blender_conn.connect()
        if success:
            return CallToolResult(
                content=[TextContent(type="text", text="✅ Successfully connected to Blender MCP server")]
            )
        else:
            return CallToolResult(
                content=[TextContent(type="text", text="❌ Failed to connect to Blender. Make sure Blender is running with MCP server enabled.")]
            )
    
    elif name == "blender_execute":
        if not blender_conn.connected:
            blender_conn.connect()
        
        code = arguments.get("code", "")
        result = blender_conn.send_command(code)
        
        if result.get("status") == "success":
            return CallToolResult(
                content=[TextContent(type="text", text=f"✅ Code executed successfully:\n{result.get('result', 'No output')}")]
            )
        else:
            return CallToolResult(
                content=[TextContent(type="text", text=f"❌ Error executing code:\n{result.get('message', 'Unknown error')}")]
            )
    
    elif name == "blender_load_infernus":
        if not blender_conn.connected:
            blender_conn.connect()
        
        # Get project root path
        project_root = Path(__file__).parent
        obj_file = project_root / "blender_export" / "infernus" / "infernus.obj"
        
        load_script = f'''
import bpy
import os

# Clear existing objects
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete(use_global=False)

# Import Infernus OBJ
obj_path = r"{obj_file}"
if os.path.exists(obj_path):
    bpy.ops.import_scene.obj(filepath=obj_path)
    print("Infernus OBJ imported successfully!")
    
    # Setup material
    for obj in bpy.context.selected_objects:
        if obj.type == 'MESH':
            mat = bpy.data.materials.new(name="Infernus_Paint")
            mat.use_nodes = True
            nodes = mat.node_tree.nodes
            principled = nodes.get("Principled BSDF")
            
            if principled:
                principled.inputs['Base Color'].default_value = (0.8, 0.1, 0.1, 1.0)  # Red
                principled.inputs['Metallic'].default_value = 0.9
                principled.inputs['Roughness'].default_value = 0.1
                principled.inputs['Clearcoat'].default_value = 1.0
                principled.inputs['Clearcoat Roughness'].default_value = 0.03
            
            obj.data.materials.append(mat)
    
    # Setup basic lighting
    bpy.ops.object.light_add(type='SUN', location=(5, 5, 10))
    sun = bpy.context.active_object
    sun.data.energy = 5
    
    # Setup camera
    bpy.ops.object.camera_add(location=(7, -7, 3))
    camera = bpy.context.active_object
    camera.rotation_euler = (1.1, 0, 0.785)
    bpy.context.scene.camera = camera
    
    print("Infernus setup complete!")
else:
    print(f"OBJ file not found: {{obj_path}}")
'''
        
        result = blender_conn.send_command(load_script)
        
        if result.get("status") == "success":
            return CallToolResult(
                content=[TextContent(type="text", text="✅ Infernus car model loaded successfully in Blender!")]
            )
        else:
            return CallToolResult(
                content=[TextContent(type="text", text=f"❌ Failed to load Infernus model:\n{result.get('message', 'Unknown error')}")]
            )
    
    elif name == "blender_setup_lighting":
        if not blender_conn.connected:
            blender_conn.connect()
        
        lighting_script = '''
import bpy

# Clear existing lights
for obj in bpy.data.objects:
    if obj.type == 'LIGHT':
        bpy.data.objects.remove(obj, do_unlink=True)

# Key light (main)
bpy.ops.object.light_add(type='SUN', location=(5, 5, 10))
key_light = bpy.context.active_object
key_light.name = "Key_Light"
key_light.data.energy = 5
key_light.data.color = (1.0, 0.95, 0.8)

# Fill light (softer)
bpy.ops.object.light_add(type='AREA', location=(-3, -3, 2))
fill_light = bpy.context.active_object
fill_light.name = "Fill_Light"
fill_light.data.energy = 100
fill_light.data.size = 5
fill_light.data.color = (0.8, 0.9, 1.0)

# Rim light (back)
bpy.ops.object.light_add(type='SPOT', location=(0, 5, 3))
rim_light = bpy.context.active_object
rim_light.name = "Rim_Light"
rim_light.data.energy = 200
rim_light.data.spot_size = 1.0
rim_light.data.color = (1.0, 1.0, 0.9)

print("Studio lighting setup complete!")
'''
        
        result = blender_conn.send_command(lighting_script)
        
        if result.get("status") == "success":
            return CallToolResult(
                content=[TextContent(type="text", text="✅ Professional studio lighting setup complete!")]
            )
        else:
            return CallToolResult(
                content=[TextContent(type="text", text=f"❌ Failed to setup lighting:\n{result.get('message', 'Unknown error')}")]
            )
    
    elif name == "blender_create_animation":
        if not blender_conn.connected:
            blender_conn.connect()

        animation_script = '''
import bpy

# Find the car object
car = None
for obj in bpy.data.objects:
    if obj.type == 'MESH' and 'infernus' in obj.name.lower():
        car = obj
        break

if car:
    # Clear existing keyframes
    car.animation_data_clear()

    # Set frame range
    bpy.context.scene.frame_start = 1
    bpy.context.scene.frame_end = 120  # 4 seconds at 30fps

    # Create rotation animation
    for frame in range(1, 121):
        bpy.context.scene.frame_set(frame)
        car.rotation_euler.z = frame * 0.0524  # Full rotation in 120 frames
        car.keyframe_insert(data_path="rotation_euler", index=2)

    print("Car rotation animation created!")
else:
    print("Car object not found!")
'''

        result = blender_conn.send_command(animation_script)

        if result.get("status") == "success":
            return CallToolResult(
                content=[TextContent(type="text", text="✅ Car rotation animation created successfully!")]
            )
        else:
            return CallToolResult(
                content=[TextContent(type="text", text=f"❌ Failed to create animation:\n{result.get('message', 'Unknown error')}")]
            )

    elif name == "blender_render":
        if not blender_conn.connected:
            blender_conn.connect()

        output_path = arguments.get("output_path", "")
        if not output_path:
            project_root = Path(__file__).parent
            output_path = project_root / "blender_export" / "infernus_render.png"

        render_script = f'''
import bpy

# Set render settings
scene = bpy.context.scene
scene.render.resolution_x = 1920
scene.render.resolution_y = 1080
scene.render.filepath = r"{output_path}"
scene.render.image_settings.file_format = 'PNG'
scene.render.engine = 'CYCLES'
scene.cycles.samples = 128

# Render
bpy.ops.render.render(write_still=True)
print(f"Render saved to: {output_path}")
'''

        result = blender_conn.send_command(render_script)

        if result.get("status") == "success":
            return CallToolResult(
                content=[TextContent(type="text", text=f"✅ Scene rendered successfully!\nOutput: {output_path}")]
            )
        else:
            return CallToolResult(
                content=[TextContent(type="text", text=f"❌ Failed to render:\n{result.get('message', 'Unknown error')}")]
            )

    elif name == "blender_status":
        if not blender_conn.connected:
            blender_conn.connect()

        status_script = '''
import bpy

objects_count = len(bpy.data.objects)
materials_count = len(bpy.data.materials)
lights_count = len([obj for obj in bpy.data.objects if obj.type == 'LIGHT'])
current_frame = bpy.context.scene.frame_current
render_engine = bpy.context.scene.render.engine

status_info = f"""
Blender Status:
- Objects in scene: {objects_count}
- Materials: {materials_count}
- Lights: {lights_count}
- Current frame: {current_frame}
- Render engine: {render_engine}
"""

print(status_info)
'''

        result = blender_conn.send_command(status_script)

        if result.get("status") == "success":
            return CallToolResult(
                content=[TextContent(type="text", text=f"✅ Blender Status:\n{result.get('result', 'No status info')}")]
            )
        else:
            return CallToolResult(
                content=[TextContent(type="text", text=f"❌ Failed to get status:\n{result.get('message', 'Unknown error')}")]
            )

    elif name == "blender_change_car_color":
        if not blender_conn.connected:
            blender_conn.connect()

        color = arguments.get("color", "red").lower()

        # Color mapping
        color_values = {
            "red": "(0.8, 0.1, 0.1, 1.0)",
            "blue": "(0.1, 0.1, 0.8, 1.0)",
            "green": "(0.1, 0.8, 0.1, 1.0)",
            "yellow": "(0.8, 0.8, 0.1, 1.0)",
            "black": "(0.05, 0.05, 0.05, 1.0)",
            "white": "(0.9, 0.9, 0.9, 1.0)",
            "orange": "(0.8, 0.4, 0.1, 1.0)",
            "purple": "(0.6, 0.1, 0.8, 1.0)"
        }

        color_rgb = color_values.get(color, "(0.8, 0.1, 0.1, 1.0)")  # Default to red

        color_script = f'''
import bpy

# Find car objects and change their color
car_found = False
for obj in bpy.data.objects:
    if obj.type == 'MESH':
        for mat in obj.data.materials:
            if mat and mat.use_nodes:
                principled = mat.node_tree.nodes.get('Principled BSDF')
                if principled:
                    principled.inputs['Base Color'].default_value = {color_rgb}
                    car_found = True
                    print(f"Changed {{obj.name}} color to {color}")

if car_found:
    print(f"Car color changed to {color}")
else:
    print("No car objects found to change color")
'''

        result = blender_conn.send_command(color_script)

        if result.get("status") == "success":
            return CallToolResult(
                content=[TextContent(type="text", text=f"✅ Car color changed to {color}!")]
            )
        else:
            return CallToolResult(
                content=[TextContent(type="text", text=f"❌ Failed to change car color:\n{result.get('message', 'Unknown error')}")]
            )

    else:
        return CallToolResult(
            content=[TextContent(type="text", text=f"❌ Unknown tool: {name}")]
        )

async def main():
    """Main server function"""
    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="blender-mcp-server",
                server_version="1.0.0",
                capabilities=server.get_capabilities(
                    notification_options=None,
                    experimental_capabilities={}
                )
            )
        )

if __name__ == "__main__":
    asyncio.run(main())
