# 🚗 Car Simulator - د<PERSON>يل البدء السريع

## 🎯 خيارات التشغيل

### الخيار 1: التجربة السريعة (مُوصى به للبداية)
```bash
# تشغيل المحاكي المبسط (بدون مكتبات خارجية)
run_simple_demo.bat
```

### الخيار 2: المشروع الكامل (يتطلب إعداد)
```bash
# بناء المشروع الكامل
build_simple.bat
```

## 🔧 متطلبات النظام

### للتجربة السريعة:
- ✅ Windows 10/11
- ✅ أي مترجم C++ (MinGW, MSVC, Clang)

### للمشروع الكامل:
- ✅ Visual Studio 2022 أو MinGW
- ✅ CMake 3.16+
- ✅ مكتبات GLM و GLFW (اختيارية)

## 🚀 التشغيل خطوة بخطوة

### 1. التجربة السريعة:
```
1. افتح Command Prompt في مجلد car_simulator
2. شغّل: run_simple_demo.bat
3. شاهد المحاكاة تعمل!
```

### 2. المشروع الكامل:
```
1. تأكد من تثبيت Visual Studio 2022
2. شغّل: build_simple.bat
3. انتظر انتهاء البناء
4. شغّل: build\bin\CarSimulator.exe
```

## 📊 ما ستراه في المحاكاة

### معلومات الفيزياء:
- **الوقت**: الوقت المنقضي بالثواني
- **الموضع**: إحداثيات السيارة (x, y, z)
- **السرعة**: السرعة بالكيلومتر/ساعة
- **الطاقة**: الطاقة الكلية بالجول

### مثال على الإخراج:
```
=== Simple Car Physics Simulator ===
Features: Realistic physics, engine force, aerodynamics, gravity

Car Simulator initialized successfully!
Starting realistic car simulation...

Time: 1.0s | Position: (0.00, 0.50, 16.67) | Speed: 60.0 km/h | Energy: 347222 J
Time: 2.0s | Position: (0.00, 0.50, 50.00) | Speed: 120.0 km/h | Energy: 1388889 J
Time: 3.0s | Position: (0.00, 0.50, 100.00) | Speed: 180.0 km/h | Energy: 3125000 J
...
```

## 🎮 المميزات المحاكاة

### الفيزياء الواقعية:
- ✅ **قوة المحرك**: 5000 نيوتن للأمام
- ✅ **الجاذبية**: 9.81 م/ث²
- ✅ **مقاومة الهواء**: معامل السحب 0.3
- ✅ **كتلة السيارة**: 1500 كيلوغرام
- ✅ **اصطدام الأرض**: منع السقوط تحت الأرض

### الحسابات المتقدمة:
- ✅ **الطاقة الحركية**: ½mv²
- ✅ **الطاقة الكامنة**: mgh
- ✅ **التكامل الرقمي**: طريقة أويلر
- ✅ **معادلات الحركة**: F = ma

## 🔧 استكشاف الأخطاء

### "No C++ compiler found"
**الحل:**
1. ثبّت MinGW: https://www.mingw-w64.org/
2. أو ثبّت Visual Studio Community (مجاني)
3. أعد تشغيل Command Prompt

### "Compilation failed"
**الحل:**
1. تأكد من وجود ملف simple_demo.cpp
2. تحقق من مساحة القرص الصلب
3. شغّل Command Prompt كمدير

### المحاكاة لا تعمل
**الحل:**
1. تأكد من وجود ملف simple_demo.exe
2. شغّل الملف مباشرة: `simple_demo.exe`
3. تحقق من رسائل الخطأ

## 📁 هيكل الملفات

```
car_simulator/
├── simple_demo.cpp          # المحاكي المبسط
├── run_simple_demo.bat      # سكريبت التشغيل السريع
├── CMakeLists.txt           # ملف بناء CMake
├── build_simple.bat        # سكريبت البناء الكامل
├── src/                     # المصدر الكامل
│   ├── main.cpp            # البرنامج الرئيسي
│   ├── physics/            # محرك الفيزياء
│   ├── graphics/           # محرك الرسوميات
│   └── ecs/                # نظام ECS
└── assets/                  # الأصول (نماذج، أصوات)
```

## 🎯 الخطوات التالية

بعد تجربة المحاكي الأساسي:

1. **جرب المشروع الكامل** - `build_simple.bat`
2. **استكشف الكود** - افتح `src/main.cpp`
3. **أضف نموذج السيارة** - ضع `untitled.fbx` في `assets/models/`
4. **طور المزيد** - أضف تحكم بلوحة المفاتيح

---

**🚗 استمتع بمحاكاة السيارات الواقعية!**
