#ifndef ECS_SYSTEM_H
#define ECS_SYSTEM_H

#include "Entity.h"
#include "Component.h"

#include <vector>
#include <set>
#include <typeindex>
#include <memory>

namespace ECS {

// Base class for all systems
class ISystem {
public:
    virtual ~ISystem() = default;
    virtual void update(float deltaTime) = 0;

    // Signature of components this system is interested in
    std::set<std::type_index> m_componentSignature;

    // Entities that this system processes
    std::vector<Entity> m_entities;

    // Helper to add a component type to the system's signature
    template<typename T>
    void requireComponent() {
        m_componentSignature.insert(typeid(T));
    }
};

} // namespace ECS

#endif // ECS_SYSTEM_H