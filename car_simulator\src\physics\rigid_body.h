#ifndef RIGID_BODY_H
#define RIGID_BODY_H

#include <glm/glm.hpp>
#include <glm/gtc/quaternion.hpp>
#include <glm/gtx/quaternion.hpp>

namespace Physics {

struct RigidBodyState {
    glm::vec3 position;         // Position (m)
    glm::quat orientation;      // Orientation (quaternion)
    glm::vec3 linearVelocity;   // Linear velocity (m/s)
    glm::vec3 angularVelocity;  // Angular velocity (rad/s)
};

struct RigidBodyProperties {
    float mass;                 // Mass (kg)
    glm::mat3 inertiaTensor;    // Inertia tensor in body space (kg*m^2)
    glm::mat3 invInertiaTensor; // Inverse inertia tensor in body space

    RigidBodyProperties(float m, const glm::mat3& inertia) : mass(m), inertiaTensor(inertia) {
        invInertiaTensor = glm::inverse(inertiaTensor);
    }
};

struct RigidBodyForceAccumulators {
    glm::vec3 force;            // Accumulated force (N)
    glm::vec3 torque;           // Accumulated torque (Nm)

    void clear() {
        force = glm::vec3(0.0f);
        torque = glm::vec3(0.0f);
    }
};

// Derivative structure for Runge-Kutta
struct RigidBodyDerivative {
    glm::vec3 dPosition;
    glm::quat dOrientation;
    glm::vec3 dLinearVelocity;
    glm::vec3 dAngularVelocity;
};

// Material properties for collision response
struct MaterialProperties {
    float restitution = 0.5f;      // Coefficient of restitution (0 = inelastic, 1 = elastic)
    float friction = 0.7f;         // Coefficient of friction
    float density = 1000.0f;       // Material density (kg/m³)
};

// Rigid body class
class RigidBody {
public:
    RigidBody(float mass, const glm::mat3& inertiaTensor);
    RigidBody(const RigidBodyProperties& properties);
    ~RigidBody();

    // State accessors
    const RigidBodyState& getState() const { return m_state; }
    void setState(const RigidBodyState& state) { m_state = state; }

    // Position and orientation
    glm::vec3 getPosition() const { return m_state.position; }
    void setPosition(const glm::vec3& position) { m_state.position = position; }

    glm::quat getOrientation() const { return m_state.orientation; }
    void setOrientation(const glm::quat& orientation) { m_state.orientation = glm::normalize(orientation); }

    glm::mat3 getRotationMatrix() const { return glm::mat3_cast(m_state.orientation); }

    // Velocity
    glm::vec3 getLinearVelocity() const { return m_state.linearVelocity; }
    void setLinearVelocity(const glm::vec3& velocity) { m_state.linearVelocity = velocity; }

    glm::vec3 getAngularVelocity() const { return m_state.angularVelocity; }
    void setAngularVelocity(const glm::vec3& angularVelocity) { m_state.angularVelocity = angularVelocity; }

    // Properties
    float getMass() const { return m_properties.mass; }
    void setMass(float mass);

    float getInverseMass() const { return m_inverseMass; }

    glm::mat3 getInertiaTensor() const { return m_properties.inertiaTensor; }
    glm::mat3 getInverseInertiaTensor() const { return m_properties.invInertiaTensor; }

    // World space inertia tensor
    glm::mat3 getWorldInertiaTensor() const;
    glm::mat3 getWorldInverseInertiaTensor() const;

    // Material properties
    const MaterialProperties& getMaterialProperties() const { return m_materialProperties; }
    void setMaterialProperties(const MaterialProperties& properties) { m_materialProperties = properties; }

    // Force and torque application
    void applyForce(const glm::vec3& force);
    void applyForceAtPoint(const glm::vec3& force, const glm::vec3& worldPoint);
    void applyTorque(const glm::vec3& torque);
    void applyImpulse(const glm::vec3& impulse);
    void applyImpulseAtPoint(const glm::vec3& impulse, const glm::vec3& worldPoint);

    // Integration
    void integrate(const glm::vec3& totalForce, const glm::vec3& totalTorque, float deltaTime);
    void clearAccumulators();

    // Utility functions
    glm::vec3 getPointVelocity(const glm::vec3& worldPoint) const;
    glm::vec3 worldToLocal(const glm::vec3& worldPoint) const;
    glm::vec3 localToWorld(const glm::vec3& localPoint) const;

    // Energy calculations
    float getKineticEnergy() const;
    float getRotationalEnergy() const;
    float getTotalEnergy() const;

    // Sleeping/activation for performance optimization
    bool isAwake() const { return m_isAwake; }
    void setAwake(bool awake) { m_isAwake = awake; }

    bool canSleep() const;
    void updateSleepState(float deltaTime);

private:
    RigidBodyState m_state;
    RigidBodyProperties m_properties;
    RigidBodyForceAccumulators m_accumulators;
    MaterialProperties m_materialProperties;

    float m_inverseMass;
    bool m_isAwake = true;
    float m_sleepTimer = 0.0f;

    static constexpr float SLEEP_THRESHOLD = 0.1f;
    static constexpr float SLEEP_TIME = 1.0f;

    void updateInertiaTensor();
};

} // namespace Physics

#endif // RIGID_BODY_H