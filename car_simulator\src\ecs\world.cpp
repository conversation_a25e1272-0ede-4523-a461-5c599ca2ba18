#include "World.h"

namespace ECS {

World::World()
    : m_nextEntityId(1) // Start entity IDs from 1, 0 can be INVALID_ENTITY
{
    m_availableEntities.reserve(MAX_ENTITIES);
    for (uint32_t i = MAX_ENTITIES; i > 0; --i) {
        m_availableEntities.push_back(i);
    }
    m_entitySignatures.resize(MAX_ENTITIES + 1); // +1 for 0-indexed access, though 0 is invalid
    m_entityComponentIndices.resize(MAX_ENTITIES + 1);

    // Initialize component pools and sizes
    for (uint32_t i = 0; i < MAX_COMPONENTS; ++i) {
        m_componentPools[i].clear(); // Will be resized on first addComponent
        m_componentSizes[i] = 0;
        m_componentNextIndex[i] = 0;
    }
}

World::~World() {
    // Destructor: Clean up dynamically allocated components if any were placed via placement new
    // This is crucial if components have non-trivial destructors.
    // For now, assuming POD components or components managed by smart pointers within.
}

Entity World::createEntity() {
    if (m_availableEntities.empty()) {
        throw std::runtime_error("No more entities available. Increase MAX_ENTITIES.");
    }
    Entity newEntity = m_availableEntities.back();
    m_availableEntities.pop_back();
    m_nextEntityId = std::max(m_nextEntityId, newEntity + 1); // Keep track of max ID used

    // Reset signature and component indices for the new entity
    m_entitySignatures[newEntity].reset();
    m_entityComponentIndices[newEntity].clear();

    return newEntity;
}

void World::destroyEntity(Entity entity) {
    if (entity == INVALID_ENTITY || entity >= m_entitySignatures.size() || !m_entitySignatures[entity].any()) {
        // Entity already destroyed or invalid
        return;
    }

    // Remove all components associated with this entity
    for (uint32_t i = 0; i < MAX_COMPONENTS; ++i) {
        if (m_entitySignatures[entity].test(i)) {
            // This is a simplified removal. A proper implementation would call the component destructor
            // and handle memory compaction within the component pool for that type.
            // For now, we just reset the bit and clear the index mapping.
            m_entityComponentIndices[entity].erase(i);
        }
    }
    m_entitySignatures[entity].reset();

    // Remove entity from all systems it was part of
    for (auto& system : m_systems) {
        auto& entities = system->m_entities;
        entities.erase(std::remove(entities.begin(), entities.end(), entity), entities.end());
    }

    m_availableEntities.push_back(entity);
}

void World::update(float deltaTime) {
    for (auto& system : m_systems) {
        system->update(deltaTime);
    }
}

void World::updateSystemEntityLists(Entity entity, const ComponentSignature& newSignature) {
    for (auto& system : m_systems) {
        // Check if the entity's new signature matches the system's required signature
        bool matches = (newSignature & system->m_componentSignature) == system->m_componentSignature;

        auto& systemEntities = system->m_entities;
        bool isCurrentlyInSystem = std::find(systemEntities.begin(), systemEntities.end(), entity) != systemEntities.end();

        if (matches && !isCurrentlyInSystem) {
            // Add entity to system
            systemEntities.push_back(entity);
        } else if (!matches && isCurrentlyInSystem) {
            // Remove entity from system
            systemEntities.erase(std::remove(systemEntities.begin(), systemEntities.end(), entity), systemEntities.end());
        }
    }
}

} // namespace ECS