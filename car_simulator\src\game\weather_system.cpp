#include "WeatherSystem.h"
#include <iostream>
#include <algorithm> // For std::clamp

namespace Game {

WeatherSystem::WeatherSystem() : 
    m_timeAccumulator(0.0f) {
    m_weatherState.type = WeatherType::CLEAR;
    m_weatherState.precipitationIntensity = 0.0f;
    m_weatherState.temperature = 20.0f; // Default to 20 Celsius
    m_weatherState.windSpeed = 5.0f;    // Default to 5 m/s
}

void WeatherSystem::update(float deltaTime) {
    // Simulate weather changes over time or based on external factors
    m_timeAccumulator += deltaTime;

    // Example: Simple weather transition logic (can be expanded)
    // For now, we'll just update based on the set type.
    switch (m_weatherState.type) {
        case WeatherType::CLEAR:
            m_weatherState.precipitationIntensity = 0.0f;
            // No significant changes to temperature/wind for clear weather
            break;
        case WeatherType::RAINY:
            updateRainyWeather(deltaTime);
            break;
        case WeatherType::SNOWY:
            updateSnowyWeather(deltaTime);
            break;
    }

    // In a full simulation, this would also trigger visual and audio updates
    // (e.g., particle systems for rain/snow, sound effects).
}

void WeatherSystem::setWeatherType(WeatherType type) {
    m_weatherState.type = type;
    // Reset relevant parameters when changing type
    switch (type) {
        case WeatherType::CLEAR:
            m_weatherState.precipitationIntensity = 0.0f;
            m_weatherState.temperature = 20.0f;
            break;
        case WeatherType::RAINY:
            m_weatherState.precipitationIntensity = 0.5f; // Start with moderate rain
            m_weatherState.temperature = 10.0f; // Cooler for rain
            break;
        case WeatherType::SNOWY:
            m_weatherState.precipitationIntensity = 0.7f; // Start with moderate snow
            m_weatherState.temperature = -2.0f; // Below freezing for snow
            break;
    }
    std::cout << "Weather set to: ";
    switch (type) {
        case WeatherType::CLEAR: std::cout << "CLEAR"; break;
        case WeatherType::RAINY: std::cout << "RAINY"; break;
        case WeatherType::SNOWY: std::cout << "SNOWY"; break;
    }
    std::cout << std::endl;
}

float WeatherSystem::getTireGripMultiplier() const {
    float multiplier = 1.0f; // Default for clear weather

    switch (m_weatherState.type) {
        case WeatherType::CLEAR:
            multiplier = 1.0f;
            break;
        case WeatherType::RAINY:
            // Grip reduces with rain intensity
            multiplier = 1.0f - (m_weatherState.precipitationIntensity * 0.4f); // Up to 40% reduction
            break;
        case WeatherType::SNOWY:
            // Grip reduces significantly with snow
            multiplier = 1.0f - (m_weatherState.precipitationIntensity * 0.7f); // Up to 70% reduction
            break;
    }
    return std::clamp(multiplier, 0.1f, 1.0f); // Ensure multiplier is within reasonable bounds
}

void WeatherSystem::updateRainyWeather(float deltaTime) {
    // Simulate dynamic rain intensity changes
    // For example, intensity might fluctuate or increase/decrease gradually
    // m_weatherState.precipitationIntensity = std::sin(m_timeAccumulator * 0.1f) * 0.2f + 0.5f; // Simple sine wave
    m_weatherState.precipitationIntensity = std::clamp(m_weatherState.precipitationIntensity, 0.0f, 1.0f);
    m_weatherState.temperature = std::max(m_weatherState.temperature - deltaTime * 0.1f, 5.0f); // Temperature drop
}

void WeatherSystem::updateSnowyWeather(float deltaTime) {
    // Simulate dynamic snow intensity changes
    m_weatherState.precipitationIntensity = std::clamp(m_weatherState.precipitationIntensity, 0.0f, 1.0f);
    m_weatherState.temperature = std::min(m_weatherState.temperature + deltaTime * 0.05f, 0.0f); // Temperature stays below freezing
}

} // namespace Game
