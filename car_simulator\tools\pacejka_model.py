import numpy as np
import matplotlib.pyplot as plt

def pacejka_magic_formula(alpha, B, C, D, E, Sh, Sv):
    """
    Calculates the lateral force (Fy) using the Pacejka Magic Formula.

    Args:
        alpha (float or np.array): Slip angle in radians.
        B (float): Stiffness factor.
        C (float): Shape factor.
        D (float): Peak factor.
        E (float): Curvature factor.
        Sh (float): Horizontal shift.
        Sv (float): Vertical shift.

    Returns:
        float or np.array: Lateral force (Fy).
    """
    alpha_shifted = alpha + Sh
    arctan_term = np.arctan(B * alpha_shifted)
    # The original formula uses (1 - E) * alpha_shifted + E * arctan(B * alpha_shifted)
    # but for simplicity and common usage, the formula is often simplified to:
    # D * sin(C * arctan(B * alpha_shifted - E * (B * alpha_shifted - arctan_term)))
    # Let's stick to the formula provided by the user:
    # Fy = D * sin(C * arctan(B * (1 - E) * (α + Sh) + E * arctan(B * (α + Sh)))) + Sv
    # This looks like a slightly modified version of the standard formula.
    # Let's implement it as given.

    # Term inside the inner arctan
    inner_term = B * (1 - E) * alpha_shifted + E * np.arctan(B * alpha_shifted)

    # Full term inside the sin function
    full_sin_term = C * np.arctan(inner_term)

    Fy = D * np.sin(full_sin_term) + Sv
    return Fy

if __name__ == "__main__":
    # Example usage of the Pacejka Magic Formula

    # Define Pacejka parameters (example values)
    # These values would typically be determined through tire testing data
    B_val = 10.0  # Stiffness factor
    C_val = 1.9   # Shape factor
    D_val = 1.0   # Peak factor (normalized to 1 for Fy_max)
    E_val = 0.97  # Curvature factor
    Sh_val = 0.0  # Horizontal shift
    Sv_val = 0.0  # Vertical shift

    # Generate a range of slip angles
    slip_angles_deg = np.linspace(-15, 15, 100) # Degrees
    slip_angles_rad = np.deg2rad(slip_angles_deg) # Radians

    # Calculate lateral force
    lateral_forces = pacejka_magic_formula(slip_angles_rad, B_val, C_val, D_val, E_val, Sh_val, Sv_val)

    # Plot the results
    plt.figure(figsize=(10, 6))
    plt.plot(slip_angles_deg, lateral_forces)
    plt.title('Pacejka Magic Formula for Lateral Force')
    plt.xlabel('Slip Angle (degrees)')
    plt.ylabel('Lateral Force (normalized)')
    plt.grid(True)
    plt.axvline(0, color='gray', linestyle='--', linewidth=0.8)
    plt.axhline(0, color='gray', linestyle='--', linewidth=0.8)
    plt.show()

    print(f"Example: Lateral force at 5 degrees slip angle: {pacejka_magic_formula(np.deg2rad(5), B_val, C_val, D_val, E_val, Sh_val, Sv_val):.4f}")
    print(f"Example: Lateral force at -5 degrees slip angle: {pacejka_magic_formula(np.deg2rad(-5), B_val, C_val, D_val, E_val, Sh_val, Sv_val):.4f}")