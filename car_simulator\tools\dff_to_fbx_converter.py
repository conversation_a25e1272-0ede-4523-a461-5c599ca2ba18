#!/usr/bin/env python3
"""
DFF to FBX Converter for Car Simulator
Converts RenderWare DFF files to FBX format for use in Blender/Unity
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

class DFFConverter:
    def __init__(self, project_root=None):
        if project_root is None:
            self.project_root = Path(__file__).parent.parent
        else:
            self.project_root = Path(project_root)
            
        self.assets_path = self.project_root / "assets"
        self.models_path = self.assets_path / "models" / "vehicles"
        self.tools_path = self.project_root / "tools"
        self.output_path = self.project_root / "blender_export"
        
        # Create output directory
        self.output_path.mkdir(exist_ok=True)
        
    def find_dff_files(self):
        """Find all DFF files in the vehicles directory"""
        dff_files = []
        for root, dirs, files in os.walk(self.models_path):
            for file in files:
                if file.lower().endswith('.dff'):
                    dff_files.append(Path(root) / file)
        return dff_files
        
    def find_txd_files(self):
        """Find all TXD files in the vehicles directory"""
        txd_files = []
        for root, dirs, files in os.walk(self.models_path):
            for file in files:
                if file.lower().endswith('.txd'):
                    txd_files.append(Path(root) / file)
        return txd_files
        
    def extract_textures_from_txd(self, txd_file, output_dir):
        """Extract textures from TXD file (placeholder - needs actual TXD extractor)"""
        print(f"Extracting textures from {txd_file}")
        
        # This is a placeholder - you would need a real TXD extractor tool
        # For now, we'll just copy any existing texture files
        textures_dir = txd_file.parent
        extracted_textures = []
        
        for ext in ['.jpg', '.png', '.tga', '.bmp']:
            for texture_file in textures_dir.glob(f"*{ext}"):
                if texture_file.is_file():
                    dest_file = output_dir / texture_file.name
                    shutil.copy2(texture_file, dest_file)
                    extracted_textures.append(dest_file)
                    print(f"  Copied texture: {texture_file.name}")
                    
        return extracted_textures
        
    def convert_dff_to_obj(self, dff_file, output_dir):
        """Convert DFF to OBJ format (placeholder - needs actual DFF converter)"""
        print(f"Converting {dff_file} to OBJ format")
        
        # This is a placeholder - you would need a real DFF converter
        # For demonstration, we'll create a simple OBJ file
        obj_file = output_dir / f"{dff_file.stem}.obj"
        mtl_file = output_dir / f"{dff_file.stem}.mtl"
        
        # Create a simple car-shaped OBJ (placeholder)
        obj_content = """# Car model converted from DFF
# This is a placeholder - replace with actual DFF converter

v -2.0 0.0 -4.0
v 2.0 0.0 -4.0
v 2.0 0.0 4.0
v -2.0 0.0 4.0
v -1.5 1.5 -3.0
v 1.5 1.5 -3.0
v 1.5 1.5 3.0
v -1.5 1.5 3.0

f 1 2 3 4
f 5 8 7 6
f 1 5 6 2
f 2 6 7 3
f 3 7 8 4
f 4 8 5 1

usemtl car_body
"""
        
        mtl_content = f"""# Material file for {dff_file.stem}
newmtl car_body
Ka 0.2 0.2 0.2
Kd 0.8 0.1 0.1
Ks 0.9 0.9 0.9
Ns 100
"""
        
        with open(obj_file, 'w') as f:
            f.write(obj_content)
            
        with open(mtl_file, 'w') as f:
            f.write(mtl_content)
            
        print(f"  Created OBJ: {obj_file}")
        return obj_file
        
    def create_blender_script(self, obj_file, textures, output_fbx):
        """Create Blender script to import OBJ and export FBX"""
        script_content = f'''
import bpy
import os

# Clear existing mesh objects
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete(use_global=False)

# Import OBJ
bpy.ops.import_scene.obj(filepath="{obj_file}")

# Setup materials with textures
for obj in bpy.context.selected_objects:
    if obj.type == 'MESH':
        # Create material
        mat = bpy.data.materials.new(name="CarMaterial")
        mat.use_nodes = True
        
        # Get material nodes
        nodes = mat.node_tree.nodes
        principled = nodes.get("Principled BSDF")
        
        # Add texture if available
        textures = {textures}
        if textures:
            tex_image = nodes.new(type='ShaderNodeTexImage')
            tex_image.image = bpy.data.images.load(textures[0])
            mat.node_tree.links.new(tex_image.outputs['Color'], principled.inputs['Base Color'])
        
        # Assign material to object
        obj.data.materials.append(mat)

# Export to FBX
bpy.ops.export_scene.fbx(
    filepath="{output_fbx}",
    use_selection=False,
    use_mesh_modifiers=True,
    use_armature_deform_only=True,
    add_leaf_bones=False,
    use_custom_props=True
)

print("Export completed: {output_fbx}")
'''
        
        script_file = self.output_path / "convert_script.py"
        with open(script_file, 'w') as f:
            f.write(script_content)
            
        return script_file
        
    def run_blender_conversion(self, script_file):
        """Run Blender in background mode to execute conversion script"""
        try:
            # Try to find Blender executable
            blender_paths = [
                "blender",  # If in PATH
                "C:/Program Files/Blender Foundation/Blender 3.6/blender.exe",
                "C:/Program Files/Blender Foundation/Blender 4.0/blender.exe",
                "/Applications/Blender.app/Contents/MacOS/Blender",
                "/usr/bin/blender"
            ]
            
            blender_exe = None
            for path in blender_paths:
                if shutil.which(path) or os.path.exists(path):
                    blender_exe = path
                    break
                    
            if not blender_exe:
                print("Blender not found! Please install Blender or add it to PATH")
                return False
                
            # Run Blender in background
            cmd = [blender_exe, "--background", "--python", str(script_file)]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("Blender conversion successful!")
                return True
            else:
                print(f"Blender conversion failed: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"Error running Blender: {e}")
            return False
            
    def convert_infernus(self):
        """Convert Infernus DFF to FBX"""
        print("=== Converting Infernus DFF to FBX ===")
        
        # Find Infernus files
        infernus_dff = self.models_path / "sports_car" / "infernus.dff"
        infernus_txd = self.models_path / "sports_car" / "textures" / "infernus.txd"
        
        if not infernus_dff.exists():
            print(f"Infernus DFF not found: {infernus_dff}")
            return False
            
        # Create output directory for Infernus
        infernus_output = self.output_path / "infernus"
        infernus_output.mkdir(exist_ok=True)
        
        # Extract textures
        textures = []
        if infernus_txd.exists():
            textures = self.extract_textures_from_txd(infernus_txd, infernus_output)
        
        # Convert DFF to OBJ
        obj_file = self.convert_dff_to_obj(infernus_dff, infernus_output)
        
        # Create FBX output path
        fbx_file = infernus_output / "infernus.fbx"
        
        # Create Blender conversion script
        script_file = self.create_blender_script(obj_file, [str(t) for t in textures], fbx_file)
        
        # Run Blender conversion
        success = self.run_blender_conversion(script_file)
        
        if success:
            print(f"Infernus converted successfully: {fbx_file}")
            return fbx_file
        else:
            print("Infernus conversion failed")
            return None
            
    def convert_all_vehicles(self):
        """Convert all DFF files to FBX"""
        print("=== Converting All Vehicles ===")
        
        dff_files = self.find_dff_files()
        converted_files = []
        
        for dff_file in dff_files:
            print(f"\nProcessing: {dff_file}")
            
            # Create output directory
            vehicle_name = dff_file.stem
            vehicle_output = self.output_path / vehicle_name
            vehicle_output.mkdir(exist_ok=True)
            
            # Find corresponding TXD
            txd_file = dff_file.parent / "textures" / f"{vehicle_name}.txd"
            
            # Extract textures
            textures = []
            if txd_file.exists():
                textures = self.extract_textures_from_txd(txd_file, vehicle_output)
            
            # Convert to OBJ
            obj_file = self.convert_dff_to_obj(dff_file, vehicle_output)
            
            # Convert to FBX
            fbx_file = vehicle_output / f"{vehicle_name}.fbx"
            script_file = self.create_blender_script(obj_file, [str(t) for t in textures], fbx_file)
            
            if self.run_blender_conversion(script_file):
                converted_files.append(fbx_file)
                print(f"✓ Converted: {fbx_file}")
            else:
                print(f"✗ Failed: {dff_file}")
                
        print(f"\nConversion complete! {len(converted_files)} files converted.")
        return converted_files

def main():
    converter = DFFConverter()
    
    print("Car Simulator DFF to FBX Converter")
    print("=" * 40)
    
    choice = input("Convert (1) Infernus only or (2) All vehicles? [1/2]: ")
    
    if choice == "1":
        result = converter.convert_infernus()
        if result:
            print(f"\nSuccess! Infernus FBX saved to: {result}")
            print("\nTo use in Blender:")
            print("1. Open Blender")
            print("2. File > Import > FBX")
            print(f"3. Select: {result}")
    elif choice == "2":
        results = converter.convert_all_vehicles()
        print(f"\nConverted {len(results)} vehicles to FBX format")
    else:
        print("Invalid choice!")

if __name__ == "__main__":
    main()
