#include "AudioEngine.h"
#include <iostream>
#include <fmod.hpp>

// Assuming ECS components are defined elsewhere
// #include "../ecs/Components.h"

namespace Audio {

AudioEngine::AudioEngine() : m_audioSystem(nullptr), m_listener(nullptr), m_speedOfSound(343.0f) {
    // Constructor
}

AudioEngine::~AudioEngine() {
    // Clean up FMOD system
    if (m_audioSystem) {
        reinterpret_cast<FMOD::System*>(m_audioSystem)->close();
        reinterpret_cast<FMOD::System*>(m_audioSystem)->release();
    }
}

bool AudioEngine::initialize() {
    FMOD::System* system;
    FMOD_RESULT result = FMOD::System_Create(&system);
    if (result != FMOD_OK) {
        std::cerr << "FMOD error! (System_Create) " << FMOD_ErrorString(result) << std::endl;
        return false;
    }

    result = system->init(512, FMOD_INIT_NORMAL, nullptr);
    if (result != FMOD_OK) {
        std::cerr << "FMOD error! (System::init) " << FMOD_ErrorString(result) << std::endl;
        return false;
    }

    m_audioSystem = system;
    std::cout << "AudioEngine initialized (FMOD)." << std::endl;
    return true;
}

void AudioEngine::update(float deltaTime) {
    if (m_audioSystem) {
        reinterpret_cast<FMOD::System*>(m_audioSystem)->update();
    }
}

void* AudioEngine::loadSound(const std::string& filePath) {
    if (!m_audioSystem) {
        std::cerr << "AudioEngine not initialized. Cannot load sound." << std::endl;
        return nullptr;
    }

    FMOD::Sound* sound;
    FMOD_RESULT result = reinterpret_cast<FMOD::System*>(m_audioSystem)->createSound(
        filePath.c_str(), FMOD_DEFAULT, nullptr, &sound);
    if (result != FMOD_OK) {
        std::cerr << "FMOD error! (createSound) " << FMOD_ErrorString(result) << std::endl;
        return nullptr;
    }
    std::cout << "Loaded sound: " << filePath << std::endl;
    return sound;
}

void AudioEngine::playSound(void* sound, const glm::vec3& position, bool loop) {
    if (!m_audioSystem || !sound) {
        std::cerr << "Cannot play sound. AudioEngine not initialized or sound is null." << std::endl;
        return;
    }

    FMOD::Channel* channel = nullptr;
    FMOD_RESULT result = reinterpret_cast<FMOD::System*>(m_audioSystem)->playSound(
        reinterpret_cast<FMOD::Sound*>(sound), nullptr, true, &channel);
    if (result != FMOD_OK) {
        std::cerr << "FMOD error! (playSound) " << FMOD_ErrorString(result) << std::endl;
        return;
    }

    if (channel) {
        FMOD_VECTOR pos = {position.x, position.y, position.z};
        channel->set3DAttributes(&pos, nullptr);
        channel->setMode(loop ? FMOD_LOOP_NORMAL : FMOD_LOOP_OFF);
        channel->setPaused(false);
    }
}

void AudioEngine::setListenerAttributes(
    const glm::vec3& position,
    const glm::vec3& forward,
    const glm::vec3& up,
    const glm::vec3& velocity
) {
    if (!m_audioSystem) return;

    FMOD_VECTOR pos = {position.x, position.y, position.z};
    FMOD_VECTOR fwd = {forward.x, forward.y, forward.z};
    FMOD_VECTOR u = {up.x, up.y, up.z};
    FMOD_VECTOR vel = {velocity.x, velocity.y, velocity.z};

    reinterpret_cast<FMOD::System*>(m_audioSystem)->set3DListenerAttributes(0, &pos, &vel, &fwd, &u);
}

float AudioEngine::calculateDopplerPitchShift(
    const glm::vec3& sourcePosition,
    const glm::vec3& sourceVelocity,
    const glm::vec3& listenerPosition,
    const glm::vec3& listenerVelocity
) {
    // Simplified Doppler effect calculation for demonstration.
    // FMOD/Wwise handle this internally when 3D attributes are set.
    // This function would be more relevant if implementing Doppler manually.

    glm::vec3 relativeVelocity = sourceVelocity - listenerVelocity;
    glm::vec3 sourceToListener = listenerPosition - sourcePosition;
    float distance = glm::length(sourceToListener);

    if (distance == 0.0f) return 1.0f; // No shift if at same position

    glm::vec3 normalizedSourceToListener = sourceToListener / distance;

    float speedOfSound = m_speedOfSound;

    float dopplerFactor = 1.0f;
    if (speedOfSound > 0.0f) {
        float sourceSpeedComponent = glm::dot(relativeVelocity, normalizedSourceToListener);
        dopplerFactor = (speedOfSound + sourceSpeedComponent) / speedOfSound;
    }

    return dopplerFactor;
}

} // namespace Audio