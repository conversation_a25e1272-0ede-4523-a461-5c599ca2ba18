# 🚗🎮 لعبة سيارات Unity - دليل كامل

## 🎯 الهدف
إنشاء لعبة سيارات تفاعلية في Unity يمكنك قيادتها بالفعل!

## 📋 المتطلبات
1. **Unity Hub** - حمّل من: https://unity.com/download
2. **Unity 2022.3 LTS** - النسخة المستقرة
3. **نموذج السيارة** - untitled.fbx (لديك بالفعل)

## 🚀 خطوات الإعداد

### 1. إنشاء مشروع جديد
```
1. افتح Unity Hub
2. اضغط "New Project"
3. اختر "3D (Built-in Render Pipeline)"
4. اسم المشروع: "CarSimulator"
5. اضغط "Create Project"
```

### 2. إعداد المشهد الأساسي
```
1. احذف الكائن الافتراضي "Cube"
2. أنشئ أرضية: GameObject > 3D Object > Plane
3. قم بتكبيرها: Scale (50, 1, 50)
4. أضف مادة للأرضية (لون رمادي)
```

### 3. استيراد نموذج السيارة
```
1. اسحب ملف untitled.fbx إلى مجلد Assets
2. اسحب النموذج من Assets إلى المشهد
3. ضعه على الأرضية (Y = 0.5 تقريباً)
```

## 🎮 سكريبت التحكم في السيارة

سأنشئ لك سكريبت كامل للتحكم في السيارة:

### CarController.cs
```csharp
using UnityEngine;

public class CarController : MonoBehaviour
{
    [Header("Car Settings")]
    public float motorForce = 1500f;
    public float brakeForce = 3000f;
    public float maxSteerAngle = 30f;
    
    [Header("Wheels")]
    public WheelCollider frontLeftWheelCollider;
    public WheelCollider frontRightWheelCollider;
    public WheelCollider rearLeftWheelCollider;
    public WheelCollider rearRightWheelCollider;
    
    [Header("Wheel Meshes")]
    public Transform frontLeftWheelTransform;
    public Transform frontRightWheelTransform;
    public Transform rearLeftWheelTransform;
    public Transform rearRightWheelTransform;
    
    private float horizontalInput;
    private float verticalInput;
    private float steerAngle;
    private bool isBraking;
    
    private Rigidbody carRigidbody;
    
    void Start()
    {
        carRigidbody = GetComponent<Rigidbody>();
        carRigidbody.centerOfMass = new Vector3(0, -0.5f, 0.5f);
    }
    
    void Update()
    {
        GetInput();
        HandleMotor();
        HandleSteering();
        UpdateWheels();
        ShowCarInfo();
    }
    
    void GetInput()
    {
        horizontalInput = Input.GetAxis("Horizontal");
        verticalInput = Input.GetAxis("Vertical");
        isBraking = Input.GetKey(KeyCode.Space);
    }
    
    void HandleMotor()
    {
        frontLeftWheelCollider.motorTorque = verticalInput * motorForce;
        frontRightWheelCollider.motorTorque = verticalInput * motorForce;
        
        float currentBrakeForce = isBraking ? brakeForce : 0f;
        
        frontLeftWheelCollider.brakeTorque = currentBrakeForce;
        frontRightWheelCollider.brakeTorque = currentBrakeForce;
        rearLeftWheelCollider.brakeTorque = currentBrakeForce;
        rearRightWheelCollider.brakeTorque = currentBrakeForce;
    }
    
    void HandleSteering()
    {
        steerAngle = maxSteerAngle * horizontalInput;
        frontLeftWheelCollider.steerAngle = steerAngle;
        frontRightWheelCollider.steerAngle = steerAngle;
    }
    
    void UpdateWheels()
    {
        UpdateSingleWheel(frontLeftWheelCollider, frontLeftWheelTransform);
        UpdateSingleWheel(frontRightWheelCollider, frontRightWheelTransform);
        UpdateSingleWheel(rearLeftWheelCollider, rearLeftWheelTransform);
        UpdateSingleWheel(rearRightWheelCollider, rearRightWheelTransform);
    }
    
    void UpdateSingleWheel(WheelCollider wheelCollider, Transform wheelTransform)
    {
        Vector3 pos;
        Quaternion rot;
        wheelCollider.GetWorldPose(out pos, out rot);
        wheelTransform.rotation = rot;
        wheelTransform.position = pos;
    }
    
    void ShowCarInfo()
    {
        float speed = carRigidbody.velocity.magnitude * 3.6f; // km/h
        
        // عرض المعلومات على الشاشة
        if (GUI.enabled)
        {
            GUI.Label(new Rect(10, 10, 200, 20), $"السرعة: {speed:F1} كم/ساعة");
            GUI.Label(new Rect(10, 30, 200, 20), $"المحرك: {(verticalInput > 0 ? "تسارع" : verticalInput < 0 ? "رجوع" : "خامل")}");
            GUI.Label(new Rect(10, 50, 200, 20), $"المكابح: {(isBraking ? "مفعلة" : "غير مفعلة")}");
            GUI.Label(new Rect(10, 70, 200, 20), "التحكم: WASD + مسطرة للمكابح");
        }
    }
}
```

## 🛠️ خطوات التطبيق في Unity

### 1. إنشاء السكريبت
```
1. في مجلد Assets، اضغط يمين > Create > C# Script
2. اسمه "CarController"
3. انسخ الكود أعلاه والصقه
```

### 2. إعداد السيارة
```
1. اختر نموذج السيارة في المشهد
2. أضف Component: Rigidbody
   - Mass: 1500
   - Drag: 0.3
   - Angular Drag: 3
3. أضف Component: CarController (السكريبت)
```

### 3. إضافة Wheel Colliders
```
1. أنشئ 4 كائنات فارغة كأطفال للسيارة:
   - FrontLeftWheel
   - FrontRightWheel  
   - RearLeftWheel
   - RearRightWheel

2. لكل عجلة، أضف Component: Wheel Collider
   - Radius: 0.3
   - Wheel Damping Rate: 1
   - Suspension Distance: 0.2
   - Force App Point Distance: 0
   - Mass: 20
```

### 4. ربط العجلات بالسكريبت
```
1. اختر السيارة
2. في CarController Component:
   - اسحب كل Wheel Collider إلى المكان المناسب
   - اسحب نماذج العجلات (إذا كانت منفصلة)
```

### 5. إعداد الكاميرا
```
1. اختر Main Camera
2. اجعلها تتبع السيارة:
   - Position: (0, 5, -10) نسبة للسيارة
   - Rotation: (20, 0, 0)
3. أو أضف سكريبت CameraFollow
```

## 🎮 التحكم في اللعبة

- **W/S**: تسارع/رجوع
- **A/D**: يسار/يمين  
- **مسطرة**: مكابح
- **ESC**: خروج

## 🎨 تحسينات إضافية

### إضافة أصوات:
```
1. أضف Audio Source للسيارة
2. أضف ملفات صوتية للمحرك
3. اربطها بالسكريبت
```

### إضافة تأثيرات:
```
1. Particle System للعادم
2. Trail Renderer لآثار الإطارات
3. إضاءة للمصابيح
```

### إضافة UI:
```
1. Canvas للواجهة
2. عداد السرعة
3. خريطة صغيرة
```

## 🏁 النتيجة النهائية

ستحصل على:
- ✅ سيارة قابلة للقيادة
- ✅ فيزياء واقعية
- ✅ تحكم سلس
- ✅ معلومات مباشرة
- ✅ تجربة تفاعلية حقيقية!

## 📁 الملفات المتوفرة

### السكريپتات الأساسية:
- ✅ `CarController.cs` - التحكم الكامل في السيارة
- ✅ `CameraFollow.cs` - كاميرا تتبع بأوضاع متعددة
- ✅ `CarUI.cs` - واجهة مستخدم متقدمة
- ✅ `CarAudio.cs` - نظام صوت واقعي

### الأدلة:
- ✅ `SETUP_GUIDE.md` - دليل الإعداد التفصيلي
- ✅ `README.md` - هذا الملف

## 🎯 المميزات الكاملة

### 🚗 فيزياء السيارة:
- محرك بقوة 1500 نيوتن
- مكابح واقعية
- توجيه دقيق
- مركز كتلة محسّن للاستقرار

### 📷 نظام الكاميرا:
- **خلف السيارة**: الوضع الافتراضي
- **منظور أول**: من داخل السيارة
- **من الأعلى**: رؤية شاملة
- **سينمائي**: حركة ديناميكية

### 🔊 نظام الصوت:
- صوت محرك متغير
- أصوات مكابح
- احتكاك الإطارات
- كلاكس (اضغط H)

### 🖥️ واجهة المستخدم:
- عداد سرعة مع إبرة
- مؤشرات الحالة
- معلومات الموضع والسرعة
- إحصائيات القيادة

## 🎮 التحكم الكامل

### التحكم الأساسي:
- **W/S**: تسارع/رجوع
- **A/D**: يسار/يمين
- **مسطرة**: مكابح
- **R**: إعادة تعيين السيارة

### التحكم المتقدم:
- **C**: تغيير وضع الكاميرا
- **H**: كلاكس
- **عجلة الماوس**: تكبير/تصغير الكاميرا
- **Escape**: خروج من اللعبة

## 🚀 البدء السريع

### 1. حمّل Unity 2022.3 LTS
### 2. أنشئ مشروع 3D جديد
### 3. اتبع SETUP_GUIDE.md خطوة بخطوة
### 4. استمتع بالقيادة!

**🎮 الآن يمكنك قيادة سيارتك في Unity بدلاً من مجرد مشاهدة الأرقام!**

**🚗✨ تجربة قيادة تفاعلية حقيقية مع فيزياء واقعية!**
