#include "lighting.h"
#include <algorithm>
#include <cmath>

namespace Graphics {

// Base Light class implementation
Light::Light(LightType type, const LightProperties& properties)
    : m_type(type), m_properties(properties) {
}

float Light::calculateAttenuation(const glm::vec3& worldPos) const {
    float distance = glm::length(worldPos - m_position);
    return 1.0f / (m_properties.constantAttenuation +
                   m_properties.linearAttenuation * distance +
                   m_properties.quadraticAttenuation * distance * distance);
}

// Color temperature to RGB conversion (Planckian locus approximation)
glm::vec3 Light::temperatureToRGB(float temperature) {
    // Clamp temperature to reasonable range
    temperature = std::clamp(temperature, 1000.0f, 40000.0f);

    float temp = temperature / 100.0f;
    glm::vec3 rgb;

    // Red component
    if (temp <= 66.0f) {
        rgb.r = 1.0f;
    } else {
        rgb.r = 1.292936f * std::pow(temp - 60.0f, -0.1332047f);
        rgb.r = std::clamp(rgb.r, 0.0f, 1.0f);
    }

    // Green component
    if (temp <= 66.0f) {
        rgb.g = 0.39008157f * std::log(temp) - 0.63184144f;
    } else {
        rgb.g = 1.292936f * std::pow(temp - 60.0f, -0.0755148f);
    }
    rgb.g = std::clamp(rgb.g, 0.0f, 1.0f);

    // Blue component
    if (temp >= 66.0f) {
        rgb.b = 1.0f;
    } else if (temp <= 19.0f) {
        rgb.b = 0.0f;
    } else {
        rgb.b = 0.543206789f * std::log(temp - 10.0f) - 1.19625408f;
        rgb.b = std::clamp(rgb.b, 0.0f, 1.0f);
    }

    return rgb;
}

// Convert lumens to intensity based on light type
float Light::lumensToIntensity(float lumens, LightType type) {
    switch (type) {
        case LightType::POINT:
            return lumens / (4.0f * M_PI); // Point light radiates in all directions
        case LightType::SPOT:
            return lumens / M_PI; // Spot light radiates in a cone
        case LightType::DIRECTIONAL:
            return lumens; // Directional light intensity is direct
        case LightType::AREA:
            return lumens / M_PI; // Area light approximation
        default:
            return lumens;
    }
}

float Light::candlelaToIntensity(float candela) {
    return candela; // Candela is already luminous intensity
}

// Directional Light implementation
DirectionalLight::DirectionalLight(const LightProperties& properties)
    : Light(LightType::DIRECTIONAL, properties) {
}

glm::vec3 DirectionalLight::calculateIllumination(const glm::vec3& worldPos, const glm::vec3& normal) const {
    if (!m_enabled) return glm::vec3(0.0f);

    // Calculate light direction (sun direction)
    glm::vec3 lightDir = -m_direction;

    // Lambert's cosine law
    float NdotL = std::max(0.0f, glm::dot(normal, lightDir));

    // Apply atmospheric scattering based on sun elevation
    glm::vec3 atmosphericColor = calculateAtmosphericColor(m_elevation);

    // Combine base color with atmospheric effects
    glm::vec3 finalColor = m_properties.color * atmosphericColor;

    return finalColor * m_properties.intensity * NdotL;
}

void DirectionalLight::setSunPosition(float azimuth, float elevation) {
    m_azimuth = azimuth;
    m_elevation = std::clamp(elevation, -90.0f, 90.0f);

    // Convert spherical coordinates to direction vector
    float azimuthRad = glm::radians(azimuth);
    float elevationRad = glm::radians(elevation);

    m_direction.x = std::cos(elevationRad) * std::sin(azimuthRad);
    m_direction.y = -std::sin(elevationRad);
    m_direction.z = std::cos(elevationRad) * std::cos(azimuthRad);
    m_direction = glm::normalize(m_direction);
}

void DirectionalLight::setTimeOfDay(float hours) {
    // Simple sun position calculation based on time
    hours = std::fmod(hours, 24.0f);

    // Sun azimuth changes throughout the day
    float azimuth = (hours - 12.0f) * 15.0f; // 15 degrees per hour

    // Sun elevation follows a sine curve
    float elevation = 90.0f * std::sin(glm::radians((hours - 6.0f) * 15.0f));

    setSunPosition(azimuth, elevation);

    // Adjust color temperature based on time of day
    if (elevation < 0.0f) {
        // Night time - moon light
        m_properties.temperature = 4000.0f; // Cooler moonlight
        m_properties.intensity = 0.1f; // Much dimmer
    } else if (elevation < 10.0f) {
        // Sunrise/sunset
        m_properties.temperature = 2000.0f + (elevation / 10.0f) * 4500.0f;
        m_properties.intensity = 0.3f + (elevation / 10.0f) * 0.7f;
    } else {
        // Daytime
        m_properties.temperature = 6500.0f; // Daylight
        m_properties.intensity = 1.0f;
    }

    // Update color based on temperature
    m_properties.color = temperatureToRGB(m_properties.temperature);
}

glm::vec3 DirectionalLight::calculateAtmosphericColor(float elevation) const {
    // Rayleigh scattering approximation
    float sunAngle = std::abs(elevation);

    if (sunAngle < 0.0f) {
        return glm::vec3(0.1f, 0.1f, 0.3f); // Night sky
    }

    // Atmospheric thickness factor
    float atmosphericThickness = 1.0f / std::max(0.1f, std::sin(glm::radians(sunAngle)));

    // Scattering coefficients (wavelength dependent)
    glm::vec3 scattering = glm::vec3(0.58f, 0.42f, 0.2f) * atmosphericThickness;

    // Apply Beer's law for atmospheric absorption
    glm::vec3 transmission = glm::exp(-scattering);

    return transmission;
}

// Point Light implementation
PointLight::PointLight(const LightProperties& properties)
    : Light(LightType::POINT, properties) {
}

glm::vec3 PointLight::calculateIllumination(const glm::vec3& worldPos, const glm::vec3& normal) const {
    if (!m_enabled) return glm::vec3(0.0f);

    glm::vec3 lightDir = glm::normalize(m_position - worldPos);
    float NdotL = std::max(0.0f, glm::dot(normal, lightDir));

    float attenuation = calculateAttenuation(worldPos);

    return m_properties.color * m_properties.intensity * NdotL * attenuation;
}

float PointLight::calculateAttenuation(const glm::vec3& worldPos) const {
    float distance = glm::length(worldPos - m_position);

    // Physically-based inverse square law with practical modifications
    float attenuation = 1.0f / (m_properties.constantAttenuation +
                               m_properties.linearAttenuation * distance +
                               m_properties.quadraticAttenuation * distance * distance);

    return attenuation;
}

void PointLight::configureAsHeadlight(float brightness) {
    m_properties.luminousFlux = brightness;
    m_properties.intensity = lumensToIntensity(brightness, LightType::POINT);
    m_properties.temperature = 6000.0f; // Modern LED headlights
    m_properties.color = temperatureToRGB(m_properties.temperature);

    // Headlight-specific attenuation (shorter range, brighter)
    m_properties.constantAttenuation = 1.0f;
    m_properties.linearAttenuation = 0.045f;
    m_properties.quadraticAttenuation = 0.0075f;
}

void PointLight::configureAsStreetLight(float brightness) {
    m_properties.luminousFlux = brightness;
    m_properties.intensity = lumensToIntensity(brightness, LightType::POINT);
    m_properties.temperature = 3000.0f; // Warm street lighting
    m_properties.color = temperatureToRGB(m_properties.temperature);

    // Street light attenuation (longer range, more diffuse)
    m_properties.constantAttenuation = 1.0f;
    m_properties.linearAttenuation = 0.022f;
    m_properties.quadraticAttenuation = 0.0019f;
}

// Spot Light implementation
SpotLight::SpotLight(const LightProperties& properties)
    : Light(LightType::SPOT, properties) {
}

glm::vec3 SpotLight::calculateIllumination(const glm::vec3& worldPos, const glm::vec3& normal) const {
    if (!m_enabled) return glm::vec3(0.0f);

    glm::vec3 lightDir = glm::normalize(m_position - worldPos);
    float NdotL = std::max(0.0f, glm::dot(normal, lightDir));

    float attenuation = calculateAttenuation(worldPos);
    float spotFactor = calculateSpotFactor(worldPos);

    return m_properties.color * m_properties.intensity * NdotL * attenuation * spotFactor;
}

float SpotLight::calculateAttenuation(const glm::vec3& worldPos) const {
    return Light::calculateAttenuation(worldPos);
}

float SpotLight::calculateSpotFactor(const glm::vec3& worldPos) const {
    glm::vec3 lightToPoint = glm::normalize(worldPos - m_position);
    float cosAngle = glm::dot(lightToPoint, m_direction);

    float innerCone = std::cos(glm::radians(m_properties.innerConeAngle));
    float outerCone = std::cos(glm::radians(m_properties.outerConeAngle));

    if (cosAngle > innerCone) {
        return 1.0f; // Full intensity
    } else if (cosAngle > outerCone) {
        // Smooth falloff between inner and outer cone
        float factor = (cosAngle - outerCone) / (innerCone - outerCone);
        return factor * factor; // Quadratic falloff for smooth transition
    } else {
        return 0.0f; // Outside cone
    }
}

void SpotLight::configureAsHeadlightBeam(float beamWidth) {
    m_properties.innerConeAngle = beamWidth * 0.7f;
    m_properties.outerConeAngle = beamWidth;
    m_properties.temperature = 6000.0f; // LED headlight
    m_properties.color = temperatureToRGB(m_properties.temperature);

    // High intensity for headlight beam
    m_properties.intensity = 5000.0f;
}

void SpotLight::configureAsFogLight(float beamWidth) {
    m_properties.innerConeAngle = beamWidth * 0.8f;
    m_properties.outerConeAngle = beamWidth;
    m_properties.temperature = 3200.0f; // Warmer fog light
    m_properties.color = temperatureToRGB(m_properties.temperature);

    // Lower intensity, wider spread for fog penetration
    m_properties.intensity = 2000.0f;
}

// Area Light implementation
AreaLight::AreaLight(const LightProperties& properties, const glm::vec2& size)
    : Light(LightType::AREA, properties), m_size(size) {
}

glm::vec3 AreaLight::calculateIllumination(const glm::vec3& worldPos, const glm::vec3& normal) const {
    if (!m_enabled) return glm::vec3(0.0f);

    // Simplified area light calculation
    // In a real implementation, this would integrate over the area
    glm::vec3 lightDir = glm::normalize(m_position - worldPos);
    float NdotL = std::max(0.0f, glm::dot(normal, lightDir));

    float distance = glm::length(worldPos - m_position);
    float area = m_size.x * m_size.y;

    // Area light attenuation considers the size of the light source
    float attenuation = area / (4.0f * M_PI * distance * distance);
    attenuation = std::min(attenuation, 1.0f); // Clamp to prevent over-brightening

    return m_properties.color * m_properties.intensity * NdotL * attenuation;
}

float AreaLight::calculateSoftShadowFactor(const glm::vec3& worldPos) const {
    // Simplified soft shadow calculation
    // Real implementation would use multiple shadow samples across the area
    float distance = glm::length(worldPos - m_position);
    float lightRadius = std::max(m_size.x, m_size.y) * 0.5f;

    // Penumbra size based on light size and distance
    float penumbraSize = lightRadius / distance;

    // Return a factor between 0 (full shadow) and 1 (no shadow)
    // This is a simplified approximation
    return std::clamp(1.0f - penumbraSize, 0.0f, 1.0f);
}

} // namespace Graphics