#include <iostream>
#include <vector>
#include <memory>
#include <chrono>
#include <thread>
#include <iomanip>

#include <glm/glm.hpp>
#include <glm/gtc/quaternion.hpp>
#include <glm/gtx/quaternion.hpp>
#include <glm/gtx/string_cast.hpp>

#include "physics/physics_engine.h"
#include "physics/rigid_body.h"
#include "physics/tire_model.h"
#include "graphics/lighting.h"
#include "ecs/component.h"

// Car simulation class
class CarSimulator {
public:
    CarSimulator() {
        // Initialize physics engine
        m_physicsEngine = std::make_unique<Physics::PhysicsEngine>();

        // Create a car rigid body
        createCar();

        // Setup lighting
        setupLighting();

        std::cout << "Car Simulator initialized successfully!" << std::endl;
    }

    void run() {
        std::cout << "Starting realistic car simulation..." << std::endl;

        auto lastTime = std::chrono::high_resolution_clock::now();
        float totalTime = 0.0f;
        const float maxSimulationTime = 10.0f; // 10 seconds

        while (totalTime < maxSimulationTime) {
            auto currentTime = std::chrono::high_resolution_clock::now();
            float deltaTime = std::chrono::duration<float>(currentTime - lastTime).count();
            lastTime = currentTime;

            // Clamp delta time to prevent large jumps
            deltaTime = std::min(deltaTime, 1.0f / 30.0f); // Max 30 FPS minimum

            update(deltaTime);
            totalTime += deltaTime;

            // Print status every second
            static float printTimer = 0.0f;
            printTimer += deltaTime;
            if (printTimer >= 1.0f) {
                printStatus(totalTime);
                printTimer = 0.0f;
            }

            // Sleep to maintain reasonable frame rate
            std::this_thread::sleep_for(std::chrono::milliseconds(16)); // ~60 FPS
        }

        std::cout << "\nSimulation completed!" << std::endl;
    }

private:
    std::unique_ptr<Physics::PhysicsEngine> m_physicsEngine;
    std::shared_ptr<Physics::RigidBody> m_carBody;
    std::unique_ptr<Graphics::DirectionalLight> m_sunLight;
    std::vector<std::unique_ptr<Graphics::PointLight>> m_headlights;

    void createCar() {
        // Create realistic car inertia tensor
        // For a typical sedan: Ixx ≈ 500, Iyy ≈ 2000, Izz ≈ 2000 kg⋅m²
        glm::mat3 inertiaTensor(0.0f);
        inertiaTensor[0][0] = 500.0f;   // Roll inertia
        inertiaTensor[1][1] = 2000.0f;  // Pitch inertia
        inertiaTensor[2][2] = 2000.0f;  // Yaw inertia

        // Create car rigid body (1500 kg sedan)
        m_carBody = std::make_shared<Physics::RigidBody>(1500.0f, inertiaTensor);

        // Set initial position slightly above ground
        m_carBody->setPosition(glm::vec3(0.0f, 1.0f, 0.0f));

        // Add to physics engine
        m_physicsEngine->addRigidBody(m_carBody);

        std::cout << "Car created: 1500kg sedan with realistic inertia" << std::endl;
    }

    void setupLighting() {
        // Create sun light
        Graphics::LightProperties sunProperties;
        sunProperties.intensity = 100000.0f; // Bright daylight
        sunProperties.temperature = 6500.0f; // Daylight color temperature
        sunProperties.color = Graphics::Light::temperatureToRGB(sunProperties.temperature);

        m_sunLight = std::make_unique<Graphics::DirectionalLight>(sunProperties);
        m_sunLight->setTimeOfDay(14.0f); // 2 PM

        // Create car headlights
        Graphics::LightProperties headlightProperties;
        headlightProperties.intensity = 3000.0f; // 3000 lumens
        headlightProperties.temperature = 6000.0f; // LED headlight
        headlightProperties.color = Graphics::Light::temperatureToRGB(headlightProperties.temperature);

        // Left headlight
        auto leftHeadlight = std::make_unique<Graphics::PointLight>(headlightProperties);
        leftHeadlight->setPosition(glm::vec3(-0.8f, 0.7f, 2.0f)); // Front left of car
        leftHeadlight->configureAsHeadlight(3000.0f);

        // Right headlight
        auto rightHeadlight = std::make_unique<Graphics::PointLight>(headlightProperties);
        rightHeadlight->setPosition(glm::vec3(0.8f, 0.7f, 2.0f)); // Front right of car
        rightHeadlight->configureAsHeadlight(3000.0f);

        m_headlights.push_back(std::move(leftHeadlight));
        m_headlights.push_back(std::move(rightHeadlight));

        std::cout << "Lighting setup: Sun + LED headlights" << std::endl;
    }

    void update(float deltaTime) {
        // Apply engine force (simulate acceleration)
        static float engineForce = 5000.0f; // 5000N forward force
        glm::vec3 forwardDirection = glm::vec3(0.0f, 0.0f, 1.0f); // Car's forward direction
        glm::vec3 engineForceVector = m_carBody->getRotationMatrix() * (forwardDirection * engineForce);

        // Apply aerodynamic drag
        glm::vec3 velocity = m_carBody->getLinearVelocity();
        Physics::VehicleParameters vehicleParams;
        glm::vec3 dragForce = m_physicsEngine->calculateDragForce(velocity, vehicleParams);

        // Apply forces to car
        m_physicsEngine->applyForce(m_carBody, engineForceVector);
        m_physicsEngine->applyForce(m_carBody, dragForce);

        // Update physics
        m_physicsEngine->update(deltaTime);

        // Update headlight positions to follow car
        glm::vec3 carPos = m_carBody->getPosition();
        glm::mat3 carRotation = m_carBody->getRotationMatrix();

        m_headlights[0]->setPosition(carPos + carRotation * glm::vec3(-0.8f, 0.7f, 2.0f));
        m_headlights[1]->setPosition(carPos + carRotation * glm::vec3(0.8f, 0.7f, 2.0f));
    }

    void printStatus(float totalTime) {
        glm::vec3 position = m_carBody->getPosition();
        glm::vec3 velocity = m_carBody->getLinearVelocity();
        float speed = glm::length(velocity) * 3.6f; // Convert m/s to km/h

        std::cout << "Time: " << std::fixed << std::setprecision(1) << totalTime << "s | "
                  << "Position: (" << std::setprecision(2) << position.x << ", " << position.y << ", " << position.z << ") | "
                  << "Speed: " << std::setprecision(1) << speed << " km/h | "
                  << "Energy: " << std::setprecision(0) << m_carBody->getTotalEnergy() << " J" << std::endl;
    }
};

int main() {
    try {
        std::cout << "=== Realistic Car Simulator ===" << std::endl;
        std::cout << "Features: Physics-based dynamics, realistic lighting, aerodynamics" << std::endl;
        std::cout << "Press Ctrl+C to stop simulation\n" << std::endl;

        CarSimulator simulator;
        simulator.run();

        std::cout << "\nThank you for using Car Simulator!" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}