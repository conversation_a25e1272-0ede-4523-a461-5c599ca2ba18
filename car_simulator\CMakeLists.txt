cmake_minimum_required(VERSION 3.16)
project(CarSimulator VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set build type if not specified
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Add compiler flags
if(MSVC)
    add_compile_options(/W4)
else()
    add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# Find required packages
find_package(glfw3 QUIET)
find_package(glm QUIET)

# Include directories
include_directories(src)

# Collect source files
file(GLOB_RECURSE SOURCES 
    "src/*.cpp"
    "src/*.h"
    "src/*.hpp"
)

# Create executable
add_executable(${PROJECT_NAME} ${SOURCES})

# Link libraries if found
if(glfw3_FOUND)
    target_link_libraries(${PROJECT_NAME} glfw)
    target_compile_definitions(${PROJECT_NAME} PRIVATE HAVE_GLFW)
endif()

if(glm_FOUND)
    target_link_libraries(${PROJECT_NAME} glm::glm)
    target_compile_definitions(${PROJECT_NAME} PRIVATE HAVE_GLM)
endif()

# Set output directory
set_target_properties(${PROJECT_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Copy assets to build directory
file(COPY ${CMAKE_SOURCE_DIR}/assets DESTINATION ${CMAKE_BINARY_DIR})

message(STATUS "Car Simulator configured successfully!")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ standard: ${CMAKE_CXX_STANDARD}")
