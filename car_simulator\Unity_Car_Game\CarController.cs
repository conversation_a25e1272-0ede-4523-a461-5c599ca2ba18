using UnityEngine;

public class CarController : MonoBehaviour
{
    [Header("إعدادات السيارة")]
    public float motorForce = 1500f;        // قوة المحرك
    public float brakeForce = 3000f;        // قوة المكابح
    public float maxSteerAngle = 30f;       // زاوية التوجيه القصوى
    
    [Header("العجلات - Wheel Colliders")]
    public WheelCollider frontLeftWheelCollider;
    public WheelCollider frontRightWheelCollider;
    public WheelCollider rearLeftWheelCollider;
    public WheelCollider rearRightWheelCollider;
    
    [Header("نماذج العجلات - Wheel Meshes")]
    public Transform frontLeftWheelTransform;
    public Transform frontRightWheelTransform;
    public Transform rearLeftWheelTransform;
    public Transform rearRightWheelTransform;
    
    [Header("الأصوات")]
    public AudioSource engineAudioSource;
    public AudioClip engineSound;
    public AudioClip brakeSound;
    
    [Header("التأثيرات")]
    public ParticleSystem exhaustParticles;
    public TrailRenderer[] tireTrails;
    
    // متغيرات التحكم
    private float horizontalInput;
    private float verticalInput;
    private float steerAngle;
    private bool isBraking;
    private bool isReversing;
    
    // مكونات السيارة
    private Rigidbody carRigidbody;
    
    // معلومات السيارة
    private float currentSpeed;
    private float maxSpeed = 200f; // كم/ساعة
    
    void Start()
    {
        // الحصول على Rigidbody
        carRigidbody = GetComponent<Rigidbody>();
        
        // تعديل مركز الكتلة لاستقرار أفضل
        carRigidbody.centerOfMass = new Vector3(0, -0.5f, 0.5f);
        
        // إعداد الصوت
        if (engineAudioSource && engineSound)
        {
            engineAudioSource.clip = engineSound;
            engineAudioSource.loop = true;
            engineAudioSource.Play();
        }
        
        // إعداد تأثيرات العادم
        if (exhaustParticles)
        {
            exhaustParticles.Play();
        }
        
        Debug.Log("🚗 تم تهيئة السيارة بنجاح!");
    }
    
    void Update()
    {
        // قراءة المدخلات
        GetInput();
        
        // تطبيق القوى
        HandleMotor();
        HandleSteering();
        
        // تحديث العجلات
        UpdateWheels();
        
        // تحديث الأصوات والتأثيرات
        UpdateAudioAndEffects();
        
        // حساب السرعة
        CalculateSpeed();
    }
    
    void GetInput()
    {
        // التحكم بالاتجاهات (WASD أو الأسهم)
        horizontalInput = Input.GetAxis("Horizontal");
        verticalInput = Input.GetAxis("Vertical");
        
        // المكابح (مسطرة)
        isBraking = Input.GetKey(KeyCode.Space);
        
        // تحديد اتجاه الحركة
        isReversing = verticalInput < 0;
    }
    
    void HandleMotor()
    {
        // تطبيق قوة المحرك على العجلات الأمامية
        float motor = verticalInput * motorForce;
        
        frontLeftWheelCollider.motorTorque = motor;
        frontRightWheelCollider.motorTorque = motor;
        
        // تطبيق المكابح على جميع العجلات
        float currentBrakeForce = isBraking ? brakeForce : 0f;
        
        ApplyBraking(currentBrakeForce);
    }
    
    void ApplyBraking(float brakeForce)
    {
        frontLeftWheelCollider.brakeTorque = brakeForce;
        frontRightWheelCollider.brakeTorque = brakeForce;
        rearLeftWheelCollider.brakeTorque = brakeForce;
        rearRightWheelCollider.brakeTorque = brakeForce;
    }
    
    void HandleSteering()
    {
        // حساب زاوية التوجيه
        steerAngle = maxSteerAngle * horizontalInput;
        
        // تطبيق التوجيه على العجلات الأمامية
        frontLeftWheelCollider.steerAngle = steerAngle;
        frontRightWheelCollider.steerAngle = steerAngle;
    }
    
    void UpdateWheels()
    {
        // تحديث موضع ودوران كل عجلة
        UpdateSingleWheel(frontLeftWheelCollider, frontLeftWheelTransform);
        UpdateSingleWheel(frontRightWheelCollider, frontRightWheelTransform);
        UpdateSingleWheel(rearLeftWheelCollider, rearLeftWheelTransform);
        UpdateSingleWheel(rearRightWheelCollider, rearRightWheelTransform);
    }
    
    void UpdateSingleWheel(WheelCollider wheelCollider, Transform wheelTransform)
    {
        if (wheelTransform == null) return;
        
        Vector3 pos;
        Quaternion rot;
        wheelCollider.GetWorldPose(out pos, out rot);
        
        wheelTransform.position = pos;
        wheelTransform.rotation = rot;
    }
    
    void UpdateAudioAndEffects()
    {
        // تحديث صوت المحرك
        if (engineAudioSource)
        {
            float enginePitch = 1.0f + (Mathf.Abs(verticalInput) * 0.5f);
            engineAudioSource.pitch = enginePitch;
            engineAudioSource.volume = 0.3f + (Mathf.Abs(verticalInput) * 0.7f);
        }
        
        // تحديث تأثيرات العادم
        if (exhaustParticles)
        {
            var emission = exhaustParticles.emission;
            emission.rateOverTime = 10 + (Mathf.Abs(verticalInput) * 40);
        }
        
        // تحديث آثار الإطارات
        UpdateTireTrails();
    }
    
    void UpdateTireTrails()
    {
        if (tireTrails == null) return;
        
        bool shouldShowTrails = isBraking && currentSpeed > 10f;
        
        foreach (TrailRenderer trail in tireTrails)
        {
            if (trail != null)
            {
                trail.emitting = shouldShowTrails;
            }
        }
    }
    
    void CalculateSpeed()
    {
        // حساب السرعة بالكيلومتر/ساعة
        currentSpeed = carRigidbody.velocity.magnitude * 3.6f;
        
        // تحديد السرعة القصوى
        if (currentSpeed > maxSpeed)
        {
            carRigidbody.velocity = carRigidbody.velocity.normalized * (maxSpeed / 3.6f);
        }
    }
    
    // عرض معلومات السيارة على الشاشة
    void OnGUI()
    {
        // إعداد النمط العربي
        GUIStyle arabicStyle = new GUIStyle(GUI.skin.label);
        arabicStyle.fontSize = 16;
        arabicStyle.normal.textColor = Color.white;
        
        // خلفية شفافة للنص
        GUI.Box(new Rect(10, 10, 250, 120), "");
        
        // عرض المعلومات
        GUI.Label(new Rect(20, 20, 200, 20), $"السرعة: {currentSpeed:F1} كم/ساعة", arabicStyle);
        GUI.Label(new Rect(20, 40, 200, 20), $"المحرك: {GetEngineStatus()}", arabicStyle);
        GUI.Label(new Rect(20, 60, 200, 20), $"المكابح: {(isBraking ? "مفعلة" : "غير مفعلة")}", arabicStyle);
        GUI.Label(new Rect(20, 80, 200, 20), $"الاتجاه: {GetSteeringStatus()}", arabicStyle);
        GUI.Label(new Rect(20, 100, 200, 20), "التحكم: WASD + مسطرة للمكابح", arabicStyle);
    }
    
    string GetEngineStatus()
    {
        if (verticalInput > 0.1f) return "تسارع";
        if (verticalInput < -0.1f) return "رجوع";
        return "خامل";
    }
    
    string GetSteeringStatus()
    {
        if (horizontalInput > 0.1f) return "يمين";
        if (horizontalInput < -0.1f) return "يسار";
        return "مستقيم";
    }
    
    // إعادة تعيين السيارة
    void ResetCar()
    {
        transform.position = new Vector3(0, 1, 0);
        transform.rotation = Quaternion.identity;
        carRigidbody.velocity = Vector3.zero;
        carRigidbody.angularVelocity = Vector3.zero;
    }
    
    // التحكم الإضافي
    void LateUpdate()
    {
        // إعادة تعيين السيارة بالضغط على R
        if (Input.GetKeyDown(KeyCode.R))
        {
            ResetCar();
            Debug.Log("🔄 تم إعادة تعيين السيارة");
        }
        
        // خروج من اللعبة بالضغط على Escape
        if (Input.GetKeyDown(KeyCode.Escape))
        {
            Application.Quit();
            Debug.Log("👋 خروج من اللعبة");
        }
    }
}
