#!/usr/bin/env python3
"""
Simple MCP Server for Claude <PERSON>op - Blender Control
Works without complex MCP dependencies
"""

import json
import sys
import socket
import asyncio
from pathlib import Path

class SimpleBlenderMCP:
    """Simple MCP server for Blender control"""
    
    def __init__(self):
        self.blender_host = "localhost"
        self.blender_port = 9876
    
    def connect_to_blender(self):
        """Connect to Blender MCP server"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            sock.connect((self.blender_host, self.blender_port))
            return sock
        except Exception as e:
            return None
    
    def send_blender_command(self, command):
        """Send command to Blender"""
        sock = self.connect_to_blender()
        if not sock:
            return {"status": "error", "message": "Cannot connect to Blender"}
        
        try:
            message = json.dumps({"command": command}) + "\n"
            sock.send(message.encode('utf-8'))
            response = sock.recv(4096).decode('utf-8')
            sock.close()
            return json.loads(response) if response else {"status": "error", "message": "No response"}
        except Exception as e:
            sock.close()
            return {"status": "error", "message": str(e)}
    
    def handle_request(self, request):
        """Handle MCP request"""
        try:
            data = json.loads(request)
            method = data.get("method", "")
            
            if method == "tools/list":
                return self.list_tools()
            elif method == "tools/call":
                return self.call_tool(data.get("params", {}))
            else:
                return {"error": {"code": -32601, "message": "Method not found"}}
                
        except Exception as e:
            return {"error": {"code": -32700, "message": f"Parse error: {str(e)}"}}
    
    def list_tools(self):
        """List available tools"""
        tools = [
            {
                "name": "blender_connect",
                "description": "Connect to Blender MCP server",
                "inputSchema": {"type": "object", "properties": {}, "required": []}
            },
            {
                "name": "blender_execute",
                "description": "Execute Python code in Blender",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "code": {"type": "string", "description": "Python code to execute"}
                    },
                    "required": ["code"]
                }
            },
            {
                "name": "blender_load_car",
                "description": "Load Infernus car model in Blender",
                "inputSchema": {"type": "object", "properties": {}, "required": []}
            },
            {
                "name": "blender_setup_lighting",
                "description": "Setup professional lighting",
                "inputSchema": {"type": "object", "properties": {}, "required": []}
            },
            {
                "name": "blender_animate_car",
                "description": "Create car rotation animation",
                "inputSchema": {"type": "object", "properties": {}, "required": []}
            },
            {
                "name": "blender_render",
                "description": "Render the scene",
                "inputSchema": {"type": "object", "properties": {}, "required": []}
            },
            {
                "name": "blender_change_color",
                "description": "Change car color",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "color": {"type": "string", "description": "Color name (red, blue, green, etc.)"}
                    },
                    "required": ["color"]
                }
            }
        ]
        
        return {"result": {"tools": tools}}
    
    def call_tool(self, params):
        """Call a specific tool"""
        tool_name = params.get("name", "")
        arguments = params.get("arguments", {})
        
        if tool_name == "blender_connect":
            sock = self.connect_to_blender()
            if sock:
                sock.close()
                return {"result": {"content": [{"type": "text", "text": "✅ Successfully connected to Blender!"}]}}
            else:
                return {"result": {"content": [{"type": "text", "text": "❌ Cannot connect to Blender. Make sure Blender is running with MCP server."}]}}
        
        elif tool_name == "blender_execute":
            code = arguments.get("code", "")
            result = self.send_blender_command(code)
            if result.get("status") == "success":
                return {"result": {"content": [{"type": "text", "text": f"✅ Code executed:\n{result.get('result', 'No output')}"}]}}
            else:
                return {"result": {"content": [{"type": "text", "text": f"❌ Error: {result.get('message', 'Unknown error')}"}]}}
        
        elif tool_name == "blender_load_car":
            project_root = Path(__file__).parent
            obj_file = project_root / "blender_export" / "infernus" / "infernus.obj"
            
            load_script = f'''
import bpy
import os

# Clear scene
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete(use_global=False)

# Load car
obj_path = r"{obj_file}"
if os.path.exists(obj_path):
    bpy.ops.import_scene.obj(filepath=obj_path)
    
    # Setup material
    for obj in bpy.context.selected_objects:
        if obj.type == 'MESH':
            mat = bpy.data.materials.new(name="Car_Paint")
            mat.use_nodes = True
            principled = mat.node_tree.nodes.get("Principled BSDF")
            if principled:
                principled.inputs['Base Color'].default_value = (0.8, 0.1, 0.1, 1.0)
                principled.inputs['Metallic'].default_value = 0.9
                principled.inputs['Roughness'].default_value = 0.1
            obj.data.materials.append(mat)
    
    # Basic lighting and camera
    bpy.ops.object.light_add(type='SUN', location=(5, 5, 10))
    bpy.ops.object.camera_add(location=(7, -7, 3))
    camera = bpy.context.active_object
    camera.rotation_euler = (1.1, 0, 0.785)
    bpy.context.scene.camera = camera
    
    print("Car loaded successfully!")
else:
    print("Car file not found!")
'''
            
            result = self.send_blender_command(load_script)
            if result.get("status") == "success":
                return {"result": {"content": [{"type": "text", "text": "✅ Infernus car loaded successfully!"}]}}
            else:
                return {"result": {"content": [{"type": "text", "text": f"❌ Failed to load car: {result.get('message', 'Unknown error')}"}]}}
        
        elif tool_name == "blender_setup_lighting":
            lighting_script = '''
import bpy

# Clear lights
for obj in bpy.data.objects:
    if obj.type == 'LIGHT':
        bpy.data.objects.remove(obj, do_unlink=True)

# Professional lighting
bpy.ops.object.light_add(type='SUN', location=(5, 5, 10))
key_light = bpy.context.active_object
key_light.data.energy = 5

bpy.ops.object.light_add(type='AREA', location=(-3, -3, 2))
fill_light = bpy.context.active_object
fill_light.data.energy = 100
fill_light.data.size = 5

bpy.ops.object.light_add(type='SPOT', location=(0, 5, 3))
rim_light = bpy.context.active_object
rim_light.data.energy = 200

print("Professional lighting setup complete!")
'''
            
            result = self.send_blender_command(lighting_script)
            if result.get("status") == "success":
                return {"result": {"content": [{"type": "text", "text": "✅ Professional lighting setup complete!"}]}}
            else:
                return {"result": {"content": [{"type": "text", "text": f"❌ Failed to setup lighting: {result.get('message', 'Unknown error')}"}]}}
        
        elif tool_name == "blender_animate_car":
            animation_script = '''
import bpy

# Find car
car = None
for obj in bpy.data.objects:
    if obj.type == 'MESH':
        car = obj
        break

if car:
    car.animation_data_clear()
    bpy.context.scene.frame_start = 1
    bpy.context.scene.frame_end = 120
    
    for frame in range(1, 121):
        bpy.context.scene.frame_set(frame)
        car.rotation_euler.z = frame * 0.0524
        car.keyframe_insert(data_path="rotation_euler", index=2)
    
    print("Animation created!")
else:
    print("No car found!")
'''
            
            result = self.send_blender_command(animation_script)
            if result.get("status") == "success":
                return {"result": {"content": [{"type": "text", "text": "✅ Car rotation animation created!"}]}}
            else:
                return {"result": {"content": [{"type": "text", "text": f"❌ Failed to create animation: {result.get('message', 'Unknown error')}"}]}}
        
        elif tool_name == "blender_render":
            project_root = Path(__file__).parent
            output_path = project_root / "blender_export" / "render.png"
            
            render_script = f'''
import bpy

scene = bpy.context.scene
scene.render.resolution_x = 1920
scene.render.resolution_y = 1080
scene.render.filepath = r"{output_path}"
scene.render.image_settings.file_format = 'PNG'
scene.render.engine = 'CYCLES'

bpy.ops.render.render(write_still=True)
print(f"Rendered to: {output_path}")
'''
            
            result = self.send_blender_command(render_script)
            if result.get("status") == "success":
                return {"result": {"content": [{"type": "text", "text": f"✅ Scene rendered successfully!\nSaved to: {output_path}"}]}}
            else:
                return {"result": {"content": [{"type": "text", "text": f"❌ Failed to render: {result.get('message', 'Unknown error')}"}]}}
        
        elif tool_name == "blender_change_color":
            color = arguments.get("color", "red").lower()
            
            color_values = {
                "red": "(0.8, 0.1, 0.1, 1.0)",
                "blue": "(0.1, 0.1, 0.8, 1.0)",
                "green": "(0.1, 0.8, 0.1, 1.0)",
                "yellow": "(0.8, 0.8, 0.1, 1.0)",
                "black": "(0.05, 0.05, 0.05, 1.0)",
                "white": "(0.9, 0.9, 0.9, 1.0)"
            }
            
            color_rgb = color_values.get(color, "(0.8, 0.1, 0.1, 1.0)")
            
            color_script = f'''
import bpy

for obj in bpy.data.objects:
    if obj.type == 'MESH':
        for mat in obj.data.materials:
            if mat and mat.use_nodes:
                principled = mat.node_tree.nodes.get('Principled BSDF')
                if principled:
                    principled.inputs['Base Color'].default_value = {color_rgb}

print(f"Car color changed to {color}")
'''
            
            result = self.send_blender_command(color_script)
            if result.get("status") == "success":
                return {"result": {"content": [{"type": "text", "text": f"✅ Car color changed to {color}!"}]}}
            else:
                return {"result": {"content": [{"type": "text", "text": f"❌ Failed to change color: {result.get('message', 'Unknown error')}"}]}}
        
        else:
            return {"error": {"code": -32601, "message": f"Unknown tool: {tool_name}"}}

def main():
    """Main MCP server loop"""
    mcp = SimpleBlenderMCP()
    
    # Read from stdin and write to stdout (MCP protocol)
    for line in sys.stdin:
        if line.strip():
            response = mcp.handle_request(line.strip())
            print(json.dumps(response))
            sys.stdout.flush()

if __name__ == "__main__":
    main()
