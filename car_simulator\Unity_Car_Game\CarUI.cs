using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class CarUI : Mono<PERSON><PERSON><PERSON><PERSON>
{
    [Header("مراجع السيارة")]
    public CarController carController;
    
    [Header("عداد السرعة")]
    public Image speedometerNeedle;          // إبرة عداد السرعة
    public TextMeshProUGUI speedText;        // نص السرعة
    public TextMeshProUGUI maxSpeedText;     // أقصى سرعة
    
    [Header("مؤشرات الحالة")]
    public Image engineIndicator;            // مؤشر المحرك
    public Image brakeIndicator;             // مؤشر المكابح
    public Image reverseIndicator;           // مؤشر الرجوع
    
    [Header("معلومات السيارة")]
    public TextMeshProUGUI positionText;     // موضع السيارة
    public TextMeshProUGUI velocityText;     // السرعة المتجهة
    public TextMeshProUGUI gearText;         // الغيار
    
    [Header("الخريطة الصغيرة")]
    public RawImage miniMap;                 // الخريطة الصغيرة
    public Transform miniMapCamera;          // كاميرا الخريطة الصغيرة
    
    [Header("الألوان")]
    public Color activeColor = Color.green;
    public Color inactiveColor = Color.gray;
    public Color warningColor = Color.red;
    
    // متغيرات داخلية
    private float maxDisplaySpeed = 200f;    // أقصى سرعة للعرض
    private float currentSpeed;
    private Vector3 lastPosition;
    private float distanceTraveled;
    
    void Start()
    {
        // البحث عن CarController إذا لم يتم تحديده
        if (carController == null)
        {
            carController = FindObjectOfType<CarController>();
        }
        
        // تهيئة النصوص
        if (maxSpeedText != null)
        {
            maxSpeedText.text = maxDisplaySpeed.ToString("F0");
        }
        
        // تهيئة الموضع الأولي
        if (carController != null)
        {
            lastPosition = carController.transform.position;
        }
        
        Debug.Log("🖥️ تم تهيئة واجهة السيارة");
    }
    
    void Update()
    {
        if (carController == null) return;
        
        // تحديث عداد السرعة
        UpdateSpeedometer();
        
        // تحديث مؤشرات الحالة
        UpdateStatusIndicators();
        
        // تحديث معلومات السيارة
        UpdateCarInfo();
        
        // تحديث الخريطة الصغيرة
        UpdateMiniMap();
    }
    
    void UpdateSpeedometer()
    {
        // الحصول على السرعة من السيارة
        Rigidbody carRb = carController.GetComponent<Rigidbody>();
        currentSpeed = carRb.velocity.magnitude * 3.6f; // تحويل إلى كم/ساعة
        
        // تحديث نص السرعة
        if (speedText != null)
        {
            speedText.text = currentSpeed.ToString("F0");
        }
        
        // تحديث إبرة عداد السرعة
        if (speedometerNeedle != null)
        {
            float speedRatio = currentSpeed / maxDisplaySpeed;
            float needleAngle = Mathf.Lerp(45f, -225f, speedRatio); // من 45 إلى -225 درجة
            speedometerNeedle.transform.rotation = Quaternion.Euler(0, 0, needleAngle);
        }
    }
    
    void UpdateStatusIndicators()
    {
        // مؤشر المحرك
        if (engineIndicator != null)
        {
            bool engineActive = Input.GetAxis("Vertical") != 0;
            engineIndicator.color = engineActive ? activeColor : inactiveColor;
        }
        
        // مؤشر المكابح
        if (brakeIndicator != null)
        {
            bool brakeActive = Input.GetKey(KeyCode.Space);
            brakeIndicator.color = brakeActive ? warningColor : inactiveColor;
        }
        
        // مؤشر الرجوع
        if (reverseIndicator != null)
        {
            bool reverseActive = Input.GetAxis("Vertical") < 0;
            reverseIndicator.color = reverseActive ? warningColor : inactiveColor;
        }
    }
    
    void UpdateCarInfo()
    {
        Transform carTransform = carController.transform;
        Rigidbody carRb = carController.GetComponent<Rigidbody>();
        
        // تحديث موضع السيارة
        if (positionText != null)
        {
            Vector3 pos = carTransform.position;
            positionText.text = $"الموضع: ({pos.x:F1}, {pos.y:F1}, {pos.z:F1})";
        }
        
        // تحديث السرعة المتجهة
        if (velocityText != null)
        {
            Vector3 vel = carRb.velocity;
            velocityText.text = $"السرعة: ({vel.x:F1}, {vel.y:F1}, {vel.z:F1})";
        }
        
        // تحديث الغيار
        if (gearText != null)
        {
            string gear = GetCurrentGear();
            gearText.text = $"الغيار: {gear}";
        }
        
        // حساب المسافة المقطوعة
        distanceTraveled += Vector3.Distance(carTransform.position, lastPosition);
        lastPosition = carTransform.position;
    }
    
    string GetCurrentGear()
    {
        float verticalInput = Input.GetAxis("Vertical");
        
        if (verticalInput > 0.1f)
        {
            // تحديد الغيار بناءً على السرعة
            if (currentSpeed < 20f) return "1";
            else if (currentSpeed < 40f) return "2";
            else if (currentSpeed < 60f) return "3";
            else if (currentSpeed < 80f) return "4";
            else return "5";
        }
        else if (verticalInput < -0.1f)
        {
            return "R"; // رجوع
        }
        else
        {
            return "N"; // نيوترال
        }
    }
    
    void UpdateMiniMap()
    {
        if (miniMapCamera == null || carController == null) return;
        
        // تحديث موضع كاميرا الخريطة الصغيرة
        Vector3 carPos = carController.transform.position;
        miniMapCamera.position = new Vector3(carPos.x, carPos.y + 50f, carPos.z);
        
        // دوران الكاميرا لتتبع اتجاه السيارة
        Vector3 carForward = carController.transform.forward;
        miniMapCamera.rotation = Quaternion.LookRotation(Vector3.down, carForward);
    }
    
    // دالة لإظهار/إخفاء الواجهة
    public void ToggleUI()
    {
        gameObject.SetActive(!gameObject.activeSelf);
    }
    
    // دالة لإعادة تعيين الإحصائيات
    public void ResetStats()
    {
        distanceTraveled = 0f;
        lastPosition = carController.transform.position;
    }
    
    // عرض إحصائيات إضافية
    void OnGUI()
    {
        // إعداد النمط
        GUIStyle style = new GUIStyle(GUI.skin.box);
        style.fontSize = 12;
        style.normal.textColor = Color.white;
        
        // صندوق الإحصائيات
        GUI.Box(new Rect(10, Screen.height - 120, 200, 100), "");
        
        // عرض الإحصائيات
        GUI.Label(new Rect(15, Screen.height - 115, 190, 20), $"المسافة المقطوعة: {distanceTraveled:F1} م", style);
        GUI.Label(new Rect(15, Screen.height - 95, 190, 20), $"أقصى سرعة: {GetMaxSpeed():F1} كم/س", style);
        GUI.Label(new Rect(15, Screen.height - 75, 190, 20), $"متوسط السرعة: {GetAverageSpeed():F1} كم/س", style);
        GUI.Label(new Rect(15, Screen.height - 55, 190, 20), $"وقت القيادة: {Time.time:F1} ثانية", style);
        
        // أزرار التحكم
        if (GUI.Button(new Rect(15, Screen.height - 30, 80, 20), "إعادة تعيين"))
        {
            ResetStats();
        }
        
        if (GUI.Button(new Rect(105, Screen.height - 30, 80, 20), "إخفاء/إظهار"))
        {
            ToggleUI();
        }
    }
    
    float GetMaxSpeed()
    {
        // يمكن تحسين هذا بحفظ أقصى سرعة تم الوصول إليها
        return currentSpeed; // مبسط للمثال
    }
    
    float GetAverageSpeed()
    {
        // حساب متوسط السرعة
        if (Time.time > 0)
        {
            return (distanceTraveled / Time.time) * 3.6f; // تحويل إلى كم/ساعة
        }
        return 0f;
    }
}
