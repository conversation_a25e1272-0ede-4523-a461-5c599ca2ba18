#include "CarController.h"
#include <algorithm> // For std::clamp
#include <iostream>

namespace Game {

CarController::CarController()
    : m_maxSteeringAngle(glm::radians(30.0f)), // Example: 30 degrees max steering
      m_steeringSensitivity(0.1f),             // Example sensitivity
      m_maxEngineTorque(500.0f),               // Example: 500 Nm max torque
      m_maxBrakingForce(10000.0f)              // Example: 10000 N max braking force
{
    m_currentInput = {0.0f, 0.0f, 0.0f, false};
}

void CarController::updateInput(const CarControlInput& input) {
    m_currentInput.steerInput = std::clamp(input.steerInput, -1.0f, 1.0f);
    m_currentInput.throttleInput = std::clamp(input.throttleInput, 0.0f, 1.0f);
    m_currentInput.brakeInput = std::clamp(input.brakeInput, 0.0f, 1.0f);
    m_currentInput.handbrakeInput = input.handbrakeInput;
}

float CarController::calculateSteeringAngle(float currentSpeed) const {
    // Simple speed-sensitive steering: less steering at higher speeds
    // This can be a more complex curve based on vehicle dynamics.
    float speedFactor = 1.0f - std::clamp(currentSpeed / 50.0f, 0.0f, 1.0f); // Reduce steering by 50% at 50 m/s
    return m_currentInput.steerInput * m_maxSteeringAngle * speedFactor;
}

float CarController::calculateEngineTorque(float engineRPM) const {
    // Very simplified engine model: linear torque based on throttle input
    // A real engine would have a torque curve (RPM vs Torque).
    // For now, assume max torque at all RPMs when throttle is applied.
    return m_currentInput.throttleInput * m_maxEngineTorque;
}

float CarController::calculateBrakingForce() const {
    return m_currentInput.brakeInput * m_maxBrakingForce;
}

void CarController::applyForceFeedback(float forceFeedbackMagnitude) {
    // This function would interact with a force feedback API (e.g., DirectInput, XInput).
    // For now, we just print the magnitude.
    // In a real implementation, this would translate the magnitude into specific force effects
    // like constant force, spring, damper, friction, etc.
    // std::cout << "Applying Force Feedback: " << forceFeedbackMagnitude << std::endl;
}

} // namespace Game