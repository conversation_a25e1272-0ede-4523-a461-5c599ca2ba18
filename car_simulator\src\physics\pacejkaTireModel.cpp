#include "PacejkaTireModel.h"
#include <cmath>

namespace Physics {

float PacejkaTireModel::calculateLateralForce(
    float slipAngle,
    const PacejkaParameters& params
) {
    // Convert slip angle to radians if it's in degrees
    // For this example, assume slipAngle is already in radians or consistent with the formula's expectation

    float alpha = slipAngle + params.Sh;

    float B = params.B;
    float C = params.C;
    float D = params.D;
    float E = params.E;
    float Sv = params.Sv;

    float Fy = D * std::sin(C * std::atan(B * alpha - E * (B * alpha - std::atan(B * alpha))));

    // The original formula from the prompt was:
    // Fy = D * sin(C * arctan(B * (1 - E) * (α + Sh) + E * arctan(B * (α + Sh)))) + Sv
    // This is a slightly different common form. Let's stick to the one provided in the prompt.

    // Re-implementing based on the prompt's exact formula:
    float term1 = B * (1.0f - E) * (slipAngle + params.Sh);
    float term2 = E * std::atan(B * (slipAngle + params.Sh));
    Fy = D * std::sin(C * std::atan(term1 + term2)) + Sv;

    return Fy;
}

// Placeholder for longitudinal force calculation
float PacejkaTireModel::calculateLongitudinalForce(
    float slipRatio,
    const PacejkaParameters& params
) {
    // Implement longitudinal force calculation using Pacejka model
    // Similar to lateral force, but based on slip ratio
    return 0.0f; // Placeholder
}

// Placeholder for aligning torque calculation
float PacejkaTireModel::calculateAligningTorque(
    float slipAngle,
    const PacejkaParameters& params
) {
    // Implement aligning torque calculation using Pacejka model
    return 0.0f; // Placeholder
}

} // namespace Physics