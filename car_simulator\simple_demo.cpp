#include <iostream>
#include <vector>
#include <memory>
#include <chrono>
#include <thread>
#include <iomanip>
#include <cmath>

// Simple 3D vector class (replacement for glm)
struct Vec3 {
    float x, y, z;
    
    Vec3(float x = 0, float y = 0, float z = 0) : x(x), y(y), z(z) {}
    
    Vec3 operator+(const Vec3& other) const { return Vec3(x + other.x, y + other.y, z + other.z); }
    Vec3 operator-(const Vec3& other) const { return Vec3(x - other.x, y - other.y, z - other.z); }
    Vec3 operator*(float scalar) const { return Vec3(x * scalar, y * scalar, z * scalar); }
    
    float length() const { return std::sqrt(x*x + y*y + z*z); }
    Vec3 normalize() const { 
        float len = length(); 
        return len > 0 ? Vec3(x/len, y/len, z/len) : Vec3(0, 0, 0); 
    }
};

// Simple car physics simulation
class SimpleCar {
public:
    Vec3 position;
    Vec3 velocity;
    Vec3 acceleration;
    float mass;
    float engineForce;
    float dragCoefficient;
    
    SimpleCar() : position(0, 1, 0), velocity(0, 0, 0), acceleration(0, 0, 0), 
                  mass(1500.0f), engineForce(5000.0f), dragCoefficient(0.3f) {}
    
    void update(float deltaTime) {
        // Apply engine force (forward)
        Vec3 engineForceVec(0, 0, engineForce);
        
        // Apply gravity
        Vec3 gravity(0, -9.81f * mass, 0);
        
        // Apply drag force (opposite to velocity)
        Vec3 dragForce = velocity * (-dragCoefficient * velocity.length());
        
        // Ground collision (simple)
        if (position.y <= 0.5f) {
            position.y = 0.5f;
            if (velocity.y < 0) velocity.y = 0;
            gravity.y = 0; // No gravity when on ground
        }
        
        // Calculate total force
        Vec3 totalForce = engineForceVec + gravity + dragForce;
        
        // F = ma, so a = F/m
        acceleration = totalForce * (1.0f / mass);
        
        // Integrate velocity and position
        velocity = velocity + acceleration * deltaTime;
        position = position + velocity * deltaTime;
    }
    
    float getSpeedKmh() const {
        return velocity.length() * 3.6f; // Convert m/s to km/h
    }
    
    float getEnergy() const {
        float kineticEnergy = 0.5f * mass * velocity.length() * velocity.length();
        float potentialEnergy = mass * 9.81f * position.y;
        return kineticEnergy + potentialEnergy;
    }
};

// Car simulator class
class CarSimulator {
public:
    CarSimulator() {
        std::cout << "Car Simulator initialized successfully!" << std::endl;
        std::cout << "Car specs: 1500kg sedan with realistic physics" << std::endl;
    }
    
    void run() {
        std::cout << "\nStarting realistic car simulation..." << std::endl;
        std::cout << "Simulating: Engine force, gravity, air resistance, ground collision" << std::endl;
        std::cout << "Duration: 10 seconds\n" << std::endl;
        
        SimpleCar car;
        auto lastTime = std::chrono::high_resolution_clock::now();
        float totalTime = 0.0f;
        const float maxSimulationTime = 10.0f; // 10 seconds
        
        while (totalTime < maxSimulationTime) {
            auto currentTime = std::chrono::high_resolution_clock::now();
            float deltaTime = std::chrono::duration<float>(currentTime - lastTime).count();
            lastTime = currentTime;
            
            // Clamp delta time to prevent large jumps
            deltaTime = std::min(deltaTime, 1.0f / 30.0f); // Max 30 FPS minimum
            
            car.update(deltaTime);
            totalTime += deltaTime;
            
            // Print status every second
            static float printTimer = 0.0f;
            printTimer += deltaTime;
            if (printTimer >= 1.0f) {
                printStatus(car, totalTime);
                printTimer = 0.0f;
            }
            
            // Sleep to maintain reasonable frame rate
            std::this_thread::sleep_for(std::chrono::milliseconds(16)); // ~60 FPS
        }
        
        std::cout << "\nSimulation completed!" << std::endl;
        printFinalStats(car);
    }
    
private:
    void printStatus(const SimpleCar& car, float totalTime) {
        std::cout << "Time: " << std::fixed << std::setprecision(1) << totalTime << "s | "
                  << "Position: (" << std::setprecision(2) << car.position.x << ", " 
                  << car.position.y << ", " << car.position.z << ") | "
                  << "Speed: " << std::setprecision(1) << car.getSpeedKmh() << " km/h | "
                  << "Energy: " << std::setprecision(0) << car.getEnergy() << " J" << std::endl;
    }
    
    void printFinalStats(const SimpleCar& car) {
        std::cout << "\n=== Final Statistics ===" << std::endl;
        std::cout << "Final position: (" << std::setprecision(2) << car.position.x << ", " 
                  << car.position.y << ", " << car.position.z << ") meters" << std::endl;
        std::cout << "Final speed: " << std::setprecision(1) << car.getSpeedKmh() << " km/h" << std::endl;
        std::cout << "Distance traveled: " << std::setprecision(1) << car.position.z << " meters" << std::endl;
        std::cout << "Total energy: " << std::setprecision(0) << car.getEnergy() << " Joules" << std::endl;
    }
};

int main() {
    try {
        std::cout << "=== Simple Car Physics Simulator ===" << std::endl;
        std::cout << "Features: Realistic physics, engine force, aerodynamics, gravity" << std::endl;
        std::cout << "This is a simplified version that runs without external libraries" << std::endl;
        std::cout << "Press Ctrl+C to stop simulation\n" << std::endl;
        
        CarSimulator simulator;
        simulator.run();
        
        std::cout << "\nThank you for using Car Simulator!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
