# 🎮 دليل إعداد لعبة السيارات في Unity - خطوة بخطوة

## 📋 المتطلبات الأساسية

### 1. تحميل وتثبيت Unity
```
1. اذهب إلى: https://unity.com/download
2. حمّل Unity Hub
3. ثبّت Unity 2022.3 LTS (النسخة المستقرة)
4. تأكد من تحديد "Microsoft Visual Studio Community" أثناء التثبيت
```

### 2. الملفات المطلوبة
- ✅ `untitled.fbx` - نموذج السيارة (لديك بالفعل)
- ✅ `CarController.cs` - سكريپت التحكم
- ✅ `CameraFollow.cs` - سكريپت الكاميرا
- ✅ `CarUI.cs` - سكريپت الواجهة

## 🚀 خطوات الإعداد التفصيلية

### الخطوة 1: إنشاء مشروع جديد
```
1. افتح Unity Hub
2. اضغط "New Project"
3. اختر "3D (Built-in Render Pipeline)"
4. اسم المشروع: "CarSimulator"
5. اختر مكان الحفظ
6. اضغط "Create Project"
```

### الخطوة 2: إعداد المشهد الأساسي
```
1. احذف الكائن الافتراضي "Cube" من Hierarchy
2. أنشئ أرضية:
   - GameObject > 3D Object > Plane
   - اسمها "Ground"
   - Scale: (50, 1, 50)
   - Position: (0, 0, 0)
```

### الخطوة 3: إضافة مادة للأرضية
```
1. في Assets، اضغط يمين > Create > Material
2. اسمها "GroundMaterial"
3. غيّر Albedo إلى لون رمادي داكن
4. اسحب المادة على الأرضية في المشهد
```

### الخطوة 4: استيراد نموذج السيارة
```
1. أنشئ مجلد "Models" في Assets
2. اسحب ملف untitled.fbx إلى مجلد Models
3. اختر الملف في Assets
4. في Inspector:
   - تأكد من أن "Import Materials" مفعل
   - اضغط "Apply"
5. اسحب النموذج من Assets إلى المشهد
6. ضعه على الأرضية (Position Y = 0.5 تقريباً)
```

### الخطوة 5: إضافة الفيزياء للسيارة
```
1. اختر نموذج السيارة في Hierarchy
2. أضف Component: Rigidbody
   - Mass: 1500
   - Drag: 0.3
   - Angular Drag: 3
   - Use Gravity: ✓
3. أضف Component: Box Collider (إذا لم يكن موجوداً)
   - تأكد من أن الحجم مناسب للسيارة
```

### الخطوة 6: إنشاء العجلات
```
1. أنشئ 4 كائنات فارغة كأطفال للسيارة:
   - اضغط يمين على السيارة > Create Empty
   - اسمها: "FrontLeftWheel"
   - كرر للعجلات الأخرى:
     * FrontRightWheel
     * RearLeftWheel  
     * RearRightWheel

2. ضع كل عجلة في موضعها الصحيح:
   - FrontLeft: (-0.8, -0.3, 1.2)
   - FrontRight: (0.8, -0.3, 1.2)
   - RearLeft: (-0.8, -0.3, -1.2)
   - RearRight: (0.8, -0.3, -1.2)

3. لكل عجلة، أضف Component: Wheel Collider
   - Radius: 0.3
   - Wheel Damping Rate: 1
   - Suspension Distance: 0.2
   - Force App Point Distance: 0
   - Mass: 20
```

### الخطوة 7: إضافة السكريپتات
```
1. أنشئ مجلد "Scripts" في Assets
2. انسخ الملفات التالية إلى مجلد Scripts:
   - CarController.cs
   - CameraFollow.cs
   - CarUI.cs

3. اختر السيارة في Hierarchy
4. أضف Component: CarController
5. في CarController، اربط العجلات:
   - اسحب كل Wheel Collider إلى المكان المناسب
```

### الخطوة 8: إعداد الكاميرا
```
1. اختر Main Camera في Hierarchy
2. أضف Component: CameraFollow
3. في CameraFollow:
   - اسحب السيارة إلى Target
   - Distance: 10
   - Height: 5
   - Rotation Damping: 3
```

### الخطوة 9: إضافة الإضاءة
```
1. اختر Directional Light في Hierarchy
2. في Inspector:
   - Intensity: 1.5
   - Color: أبيض مائل للأصفر
   - Rotation: (30, 30, 0)
```

### الخطوة 10: إضافة Tag للسيارة
```
1. اختر السيارة
2. في Inspector، اضغط على Tag
3. اختر "Add Tag"
4. أضف Tag جديد: "Player"
5. ارجع للسيارة وحدد Tag: "Player"
```

## 🎮 اختبار اللعبة

### تشغيل اللعبة:
```
1. اضغط Play في Unity
2. استخدم WASD للتحكم
3. مسطرة للمكابح
4. C لتغيير وضع الكاميرا
5. R لإعادة تعيين السيارة
```

### التحكم:
- **W**: تسارع للأمام
- **S**: رجوع
- **A**: يسار
- **D**: يمين
- **مسطرة**: مكابح
- **C**: تغيير وضع الكاميرا
- **R**: إعادة تعيين السيارة
- **Escape**: خروج

## 🎨 تحسينات إضافية

### إضافة أصوات:
```
1. أنشئ مجلد "Audio" في Assets
2. أضف ملفات صوتية للمحرك
3. أضف Audio Source للسيارة
4. اربط الأصوات في CarController
```

### إضافة تأثيرات:
```
1. أضف Particle System للعادم:
   - GameObject > Effects > Particle System
   - اجعله طفل للسيارة
   - ضعه في مؤخرة السيارة

2. أضف Trail Renderer للإطارات:
   - أضف للعجلات الخلفية
   - Material: خط أسود
```

### إضافة واجهة مستخدم:
```
1. أنشئ Canvas:
   - GameObject > UI > Canvas
2. أضف Text للسرعة:
   - GameObject > UI > Text - TextMeshPro
3. أضف CarUI script للCanvas
```

## 🔧 حل المشاكل الشائعة

### السيارة لا تتحرك:
- تأكد من وجود Rigidbody
- تأكد من ربط Wheel Colliders
- تحقق من Mass (يجب أن تكون > 0)

### السيارة تنقلب:
- قلل Center of Mass في CarController
- زد Angular Drag في Rigidbody
- تأكد من موضع العجلات

### الكاميرا لا تتبع:
- تأكد من ربط Target في CameraFollow
- تحقق من Tag السيارة ("Player")

### الفيزياء غريبة:
- تأكد من Fixed Timestep في Project Settings
- تحقق من Scale السيارة (يجب أن تكون واقعية)

## 📊 النتيجة النهائية

بعد اتباع هذه الخطوات، ستحصل على:
- ✅ سيارة قابلة للقيادة بفيزياء واقعية
- ✅ كاميرا تتبع السيارة بأوضاع متعددة
- ✅ واجهة مستخدم تعرض معلومات السيارة
- ✅ تحكم سلس ومريح
- ✅ تجربة لعب تفاعلية حقيقية!

**🎮 الآن يمكنك قيادة سيارتك الافتراضية في Unity!** 🚗✨
