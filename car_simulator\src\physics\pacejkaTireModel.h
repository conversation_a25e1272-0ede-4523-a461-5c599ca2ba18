#ifndef PACEJKA_TIRE_MODEL_H
#define PACEJKA_TIRE_MODEL_H

#include <glm/glm.hpp>
#include <glm/gtc/constants.hpp>
#include <cmath>

namespace Physics {

struct PacejkaParameters {
    float B; // Stiffness factor
    float C; // Shape factor
    float D; // Peak factor
    float E; // Curvature factor
    float Sh; // Horizontal shift
    float Sv; // Vertical shift
};

class PacejkaTireModel {
public:
    /**
     * @brief Calculates the lateral force (Fy) using the Pacejka Magic Formula.
     *
     * Fy = D * sin(C * arctan(B * (1 - E) * (α + Sh) + E * arctan(B * (α + Sh)))) + Sv
     *
     * @param alpha Slip angle in radians.
     * @param params Pacejka parameters.
     * @return Lateral force (Fy).
     */
    static float calculateLateralForce(float alpha, const PacejkaParameters& params) {
        float alpha_shifted = alpha + params.Sh;
        float inner_term = params.B * (1.0f - params.E) * alpha_shifted + params.E * std::atan(params.B * alpha_shifted);
        float full_sin_term = params.C * std::atan(inner_term);
        float Fy = params.D * std::sin(full_sin_term) + params.Sv;
        return Fy;
    }

    // TODO: Add functions for longitudinal force (Fx) and overturning moment (Mz)
    // Fx = D * sin(C * arctan(B * (1 - E) * (kappa + Sh) + E * arctan(B * (kappa + Sh)))) + Sv
    // Mz = D * sin(C * arctan(B * (1 - E) * (alpha + Sh) + E * arctan(B * (alpha + Sh)))) + Sv

};

} // namespace Physics

#endif // PACEJKA_TIRE_MODEL_H