# 🚀 إعداد MCP مع Claude Desktop - دليل شامل

## 📋 المشكلة والحل

**المشكلة:** النظام الحالي يستخدم socket connection مباشر وليس MCP حقيقي متوافق مع Claude Desktop.

**الحل:** إنشاء MCP server حقيقي يمكن تكوينه في Claude Desktop للتحكم في Blender بدون حدود الرسائل.

## 🔧 الإعداد المطلوب

### الخطوة 1: تثبيت مكتبات MCP

```bash
cd C:\ASP\car_simulator
pip install -r mcp_requirements.txt
```

### الخطوة 2: تكوين Claude Desktop

1. **افتح Claude Desktop**
2. **اذهب إلى**: Settings > Developer
3. **Edit Config** أو انسخ المحتوى التالي إلى ملف التكوين:

**مسار ملف التكوين:**
- Windows: `%APPDATA%\Claude\claude_desktop_config.json`
- macOS: `~/Library/Application Support/Claude/claude_desktop_config.json`
- Linux: `~/.config/Claude/claude_desktop_config.json`

**محتوى ملف التكوين:**
```json
{
  "mcpServers": {
    "blender-control": {
      "command": "python",
      "args": [
        "C:/ASP/car_simulator/claude_desktop_mcp_server.py"
      ],
      "env": {
        "PYTHONPATH": "C:/ASP/car_simulator"
      }
    }
  }
}
```

### الخطوة 3: إعداد Blender

1. **افتح Blender**
2. **اذهب إلى**: Scripting workspace
3. **افتح ملف**: `blender_mcp_server.py`
4. **اضغط**: Run Script
5. **تأكد من الرسالة**: "MCP Server started on localhost:9876"

### الخطوة 4: إعادة تشغيل Claude Desktop

1. **أغلق Claude Desktop تماماً**
2. **افتح Claude Desktop مرة أخرى**
3. **تحقق من ظهور أدوات Blender في القائمة**

## 🎯 الأدوات المتاحة في Claude Desktop

بعد الإعداد الصحيح، ستظهر الأدوات التالية في Claude Desktop:

### 1. **blender_connect**
- **الوصف**: الاتصال بخادم Blender MCP
- **الاستخدام**: "اتصل بـ Blender"

### 2. **blender_execute**
- **الوصف**: تنفيذ كود Python في Blender
- **الاستخدام**: "نفذ هذا الكود في Blender: print('Hello!')"

### 3. **blender_load_infernus**
- **الوصف**: تحميل نموذج سيارة Infernus
- **الاستخدام**: "حمّل سيارة Infernus في Blender"

### 4. **blender_setup_lighting**
- **الوصف**: إعداد إضاءة استوديو احترافية
- **الاستخدام**: "أعد إضاءة احترافية في Blender"

### 5. **blender_create_animation**
- **الوصف**: إنشاء أنيميشن دوران للسيارة
- **الاستخدام**: "أنشئ أنيميشن دوران للسيارة"

### 6. **blender_render**
- **الوصف**: رندر المشهد الحالي
- **الاستخدام**: "ارندر المشهد"

### 7. **blender_status**
- **الوصف**: الحصول على معلومات حالة Blender
- **الاستخدام**: "ما هي حالة Blender؟"

### 8. **blender_change_car_color**
- **الوصف**: تغيير لون السيارة
- **الاستخدام**: "غيّر لون السيارة إلى أزرق"

## 💬 أمثلة على المحادثات

### مثال 1: إعداد مشهد كامل
```
أنت: "أريد إعداد مشهد سيارة كامل في Blender"

Claude: سأقوم بإعداد مشهد سيارة كامل لك:

1. أولاً، سأتصل بـ Blender
2. ثم سأحمل سيارة Infernus
3. سأعد إضاءة احترافية
4. سأنشئ أنيميشن دوران
5. وأخيراً سأرندر المشهد

[يستخدم Claude الأدوات تلقائياً]
```

### مثال 2: تخصيص السيارة
```
أنت: "غيّر لون السيارة إلى أزرق وأضف تأثيرات خاصة"

Claude: سأقوم بتغيير لون السيارة وإضافة تأثيرات:

[يستخدم blender_change_car_color]
[يستخدم blender_execute لإضافة تأثيرات]
```

## 🔍 استكشاف الأخطاء

### المشكلة: "MCP server not found"
**الحلول:**
1. تأكد من تثبيت مكتبات MCP: `pip install -r mcp_requirements.txt`
2. تحقق من مسار الملف في التكوين
3. تأكد من إعادة تشغيل Claude Desktop

### المشكلة: "Cannot connect to Blender"
**الحلول:**
1. تأكد من تشغيل Blender
2. تأكد من تشغيل MCP server في Blender
3. تحقق من المنفذ 9876

### المشكلة: "Tools not appearing"
**الحلول:**
1. تحقق من صحة ملف التكوين JSON
2. تأكد من إعادة تشغيل Claude Desktop
3. تحقق من مسار Python والملف

## 🌟 المميزات الجديدة

### ✅ **بدون حدود رسائل**
- استخدم Claude Desktop بدون قيود
- تحكم كامل في Blender

### ✅ **تكامل سلس**
- أدوات مدمجة في Claude Desktop
- واجهة طبيعية بالعربية والإنجليزية

### ✅ **قوة كاملة**
- تنفيذ أي كود Python في Blender
- تحكم متقدم في المشاهد

### ✅ **سهولة الاستخدام**
- أوامر طبيعية بدلاً من كود
- استجابة فورية

## 🚀 الخطوات التالية

1. **اتبع خطوات الإعداد**
2. **اختبر الاتصال**
3. **جرب الأوامر المختلفة**
4. **استمتع بالتحكم الكامل!**

---

**🎯 الآن يمكنك التحكم في Blender من Claude Desktop بدون أي حدود!** 🚗✨
