"""GLSL
#version 450 core
out vec4 FragColor;

in vec2 TexCoords;
in vec4 CurrentPos;
in vec4 PrevPos;

uniform sampler2D screenTexture;
uniform float motionBlurStrength; // Controls the intensity of the motion blur

void main()
{
    // Convert to normalized device coordinates
    vec2 currentNDC = CurrentPos.xy / CurrentPos.w;
    vec2 prevNDC = PrevPos.xy / PrevPos.w;

    // Calculate velocity vector in screen space
    vec2 velocity = currentNDC - prevNDC;

    vec4 finalColor = texture(screenTexture, TexCoords);

    // Sample along the velocity vector
    int samples = 10; // Number of samples for blur
    for (int i = 1; i < samples; ++i)
    {
        vec2 offset = velocity * (float(i) / float(samples - 1) - 0.5) * motionBlurStrength;
        finalColor += texture(screenTexture, TexCoords + offset);
    }
    finalColor /= float(samples);

    FragColor = finalColor;
}
"""

