# Vehicle Models Directory

This directory contains 3D models for vehicles in the car simulator.

## Supported Formats

### DFF Files (RenderWare)
- **Location**: Place `.dff` files directly in vehicle-specific subdirectories
- **Usage**: Main 3D geometry and mesh data
- **Example**: `sedan/car_body.dff`, `sports_car/ferrari.dff`

### TXD Files (RenderWare Textures)
- **Location**: Place `.txd` files in the `textures/` subdirectory of each vehicle
- **Usage**: Texture dictionaries containing all textures for the vehicle
- **Example**: `sedan/textures/car_textures.txd`

## Directory Structure

```
vehicles/
├── sedan/
│   ├── car_body.dff
│   ├── wheels.dff
│   └── textures/
│       ├── car_textures.txd
│       └── wheel_textures.txd
├── sports_car/
│   ├── ferrari.dff
│   ├── lamborghini.dff
│   └── textures/
│       └── sports_textures.txd
├── truck/
│   ├── pickup.dff
│   └── textures/
│       └── truck_textures.txd
└── motorcycle/
    ├── bike.dff
    └── textures/
        └── bike_textures.txd
```

## Loading in Code

```cpp
// Example of loading a vehicle model
VehicleModel sedan;
sedan.loadDFF("assets/models/vehicles/sedan/car_body.dff");
sedan.loadTextures("assets/models/vehicles/sedan/textures/car_textures.txd");
```

## Model Requirements

### Physics Properties
Each vehicle model should include:
- **Mass**: Total vehicle mass in kg
- **Center of Mass**: Position relative to model origin
- **Moment of Inertia**: Inertia tensor for realistic rotation
- **Collision Mesh**: Simplified geometry for collision detection

### Visual Properties
- **Level of Detail (LOD)**: Multiple detail levels for performance
- **Damage States**: Different models for damage visualization
- **Animation Bones**: For doors, wheels, suspension movement

## Performance Guidelines

- Keep polygon count reasonable (< 50k triangles for main LOD)
- Use texture atlases to minimize draw calls
- Implement proper LOD system for distant vehicles
- Consider using instancing for multiple identical vehicles

## File Naming Convention

- Use descriptive names: `sedan_4door.dff`, `sports_ferrari_f40.dff`
- Include vehicle type and specific model if applicable
- Use lowercase with underscores for consistency
