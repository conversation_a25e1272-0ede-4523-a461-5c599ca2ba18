#ifndef PHYSICS_ENGINE_H
#define PHYSICS_ENGINE_H

#include <vector>
#include <memory>
#include <functional>
#include <unordered_map>

#include <glm/glm.hpp>
#include <glm/gtc/quaternion.hpp>
#include <glm/gtx/quaternion.hpp>
#include <glm/gtc/matrix_transform.hpp>

#include "rigid_body.h"
#include "integrator.h"
#include "tire_model.h"

// Forward declarations for ECS components
namespace ECS {
    struct TransformComponent;
    struct RigidBodyComponent;
    struct TireComponent;
}

namespace Physics {

// Physics constants
constexpr float GRAVITY = -9.81f;
constexpr float AIR_DENSITY = 1.225f; // kg/m³ at sea level
constexpr float DRAG_COEFFICIENT = 0.3f; // Typical car drag coefficient

// Vehicle dynamics parameters
struct VehicleParameters {
    float mass = 1500.0f;                   // Vehicle mass in kg
    float wheelbase = 2.7f;                 // Distance between front and rear axles
    float trackWidth = 1.5f;                // Distance between left and right wheels
    float centerOfMassHeight = 0.5f;        // Height of center of mass above ground
    float frontAxleDistance = 1.2f;         // Distance from CoM to front axle
    float rearAxleDistance = 1.5f;          // Distance from CoM to rear axle

    // Moment of inertia tensor (simplified as diagonal matrix)
    glm::vec3 momentOfInertia = glm::vec3(2500.0f, 5000.0f, 2500.0f); // Ixx, Iyy, Izz

    // Aerodynamic properties
    float frontalArea = 2.2f;               // Frontal area in m²
    float dragCoefficient = 0.3f;           // Drag coefficient
    float liftCoefficient = 0.1f;           // Lift coefficient
};

// Force and torque accumulator
struct ForceAccumulator {
    glm::vec3 force = glm::vec3(0.0f);
    glm::vec3 torque = glm::vec3(0.0f);

    void clear() {
        force = glm::vec3(0.0f);
        torque = glm::vec3(0.0f);
    }

    void addForce(const glm::vec3& f) {
        force += f;
    }

    void addForceAtPoint(const glm::vec3& f, const glm::vec3& point, const glm::vec3& centerOfMass) {
        force += f;
        torque += glm::cross(point - centerOfMass, f);
    }

    void addTorque(const glm::vec3& t) {
        torque += t;
    }
};

class PhysicsEngine {
public:
    PhysicsEngine();
    ~PhysicsEngine();

    /**
     * @brief Updates the physics simulation for a given time step.
     * @param deltaTime The time elapsed since the last update.
     */
    void update(float deltaTime);

    /**
     * @brief Calculates the derivatives for a rigid body given its state and forces.
     * This function is passed to the RK4 integrator.
     * @param state Current state of the rigid body.
     * @param properties Properties of the rigid body (mass, inertia).
     * @param accumulators Accumulated forces and torques on the rigid body.
     * @return Calculated derivatives.
     */
    static RigidBodyDerivative calculateRigidBodyDerivative(
        const RigidBodyState& state,
        const RigidBodyProperties& properties,
        const RigidBodyForceAccumulators& accumulators
    );

    // Rigid body management
    void addRigidBody(std::shared_ptr<RigidBody> body);
    void removeRigidBody(std::shared_ptr<RigidBody> body);

    // Force application
    void applyForce(std::shared_ptr<RigidBody> body, const glm::vec3& force);
    void applyForceAtPoint(std::shared_ptr<RigidBody> body, const glm::vec3& force, const glm::vec3& point);
    void applyTorque(std::shared_ptr<RigidBody> body, const glm::vec3& torque);

    // Tire force calculations (Pacejka Magic Formula)
    glm::vec3 calculateTireForce(const TireParameters& tire, float slipRatio, float slipAngle, float normalForce);
    glm::vec3 calculateLongitudinalForce(const TireParameters& tire, float slipRatio, float normalForce);
    glm::vec3 calculateLateralForce(const TireParameters& tire, float slipAngle, float normalForce);

    // Aerodynamic forces
    glm::vec3 calculateDragForce(const glm::vec3& velocity, const VehicleParameters& vehicle);
    glm::vec3 calculateLiftForce(const glm::vec3& velocity, const VehicleParameters& vehicle);

    // Suspension forces
    glm::vec3 calculateSuspensionForce(float compression, float compressionRate,
                                      float springConstant, float dampingConstant);

    // Collision detection and response
    void handleCollisions();

    // ECS integration methods
    void applyForceToRigidBody(
        ECS::RigidBodyComponent& rbComponent,
        const glm::vec3& force,
        const glm::vec3& relativePoint = glm::vec3(0.0f, 0.0f, 0.0f)
    );

    void calculateTireForces(
        ECS::TireComponent& tireComponent,
        ECS::RigidBodyComponent& rbComponent,
        ECS::TransformComponent& transformComponent
    );

    // Getters/Setters
    void setGravity(const glm::vec3& gravity) { m_gravity = gravity; }
    glm::vec3 getGravity() const { return m_gravity; }

    void setAirDensity(float density) { m_airDensity = density; }
    float getAirDensity() const { return m_airDensity; }

private:
    std::vector<std::shared_ptr<RigidBody>> m_rigidBodies;
    std::unordered_map<std::shared_ptr<RigidBody>, ForceAccumulator> m_forceAccumulators;

    glm::vec3 m_gravity = glm::vec3(0.0f, GRAVITY, 0.0f);
    float m_airDensity = AIR_DENSITY;

    // Internal helper functions
    float pacejkaFormula(float input, float B, float C, float D, float E, float Sh, float Sv);
    void integrateRigidBodies(float deltaTime);
    void clearForceAccumulators();
};

} // namespace Physics

#endif // PHYSICS_ENGINE_H