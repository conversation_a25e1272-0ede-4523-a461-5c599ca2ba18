#version 450 core

layout (location = 0) in vec3 aPos;
layout (location = 1) in vec3 aNormal;
layout (location = 2) in vec2 aTexCoords;

uniform mat4 projection;
uniform mat4 view;
uniform mat4 model;
uniform mat4 prevModelViewProjection; // Previous frame's MVP matrix

out vec2 TexCoords;
out vec4 CurrentPos;
out vec4 PrevPos;

void main()
{
    TexCoords = aTexCoords;
    CurrentPos = projection * view * model * vec4(aPos, 1.0);
    PrevPos = prevModelViewProjection * vec4(aPos, 1.0);
    gl_Position = CurrentPos;
}


