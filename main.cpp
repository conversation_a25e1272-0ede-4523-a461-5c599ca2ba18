#include <iostream>
#include <vector>
#include <memory>

#include <glm/glm.hpp>
#include <glm/gtc/quaternion.hpp>
#include <glm/gtx/quaternion.hpp>
#include <glm/gtx/string_cast.hpp>

#include "physics/PhysicsEngine.h"
#include "ecs/Components.h"

// A very basic entity manager for demonstration purposes
// In a real ECS, this would be more sophisticated.
struct SimpleEntityManager {
    std::vector<ECS::TransformComponent> transformComponents;
    std::vector<ECS::RigidBodyComponent> rigidBodyComponents;
    std::vector<ECS::TireComponent> tireComponents;

    // For simplicity, entity ID will be the index in these vectors
    // This is NOT a proper ECS implementation but serves for a quick demo.
    int createEntity() {
        transformComponents.emplace_back();
        rigidBodyComponents.emplace_back();
        tireComponents.emplace_back(10.0f, 1.9f, 1.0f, 0.97f, 0.0f, 0.0f); // Add a dummy tire component with example Pacejka params
        return transformComponents.size() - 1;
    }
};

int main() {
    std::cout << "Starting Car Simulator Physics Demo..." << std::endl;

    SimpleEntityManager entityManager;
    int carEntityId = entityManager.createEntity();

    // Get references to the components for the car entity
    ECS::TransformComponent& carTransform = entityManager.transformComponents[carEntityId];
    ECS::RigidBodyComponent& carRigidBody = entityManager.rigidBodyComponents[carEntityId];
    ECS::TireComponent& carTire = entityManager.tireComponents[carEntityId];

    // Initialize car properties
    carRigidBody.properties = Physics::RigidBodyProperties(1500.0f, glm::mat3(1000.0f)); // 1500 kg, dummy inertia
    carTransform.position = glm::vec3(0.0f, 1.0f, 0.0f); // Start slightly above ground

    // Initialize Pacejka parameters for the tire (example values)
    carTire = ECS::TireComponent(10.0f, 1.9f, 1.0f, 0.97f, 0.0f, 0.0f);

    Physics::PhysicsEngine physicsEngine;

    float deltaTime = 1.0f / 60.0f; // 60 FPS
    float simulationTime = 0.0f;
    float totalSimulationDuration = 5.0f; // Simulate for 5 seconds

    std::cout << "\nSimulation started..." << std::endl;

    while (simulationTime < totalSimulationDuration) {
        // Clear accumulators for this frame
        carRigidBody.clearAccumulators();

        // Apply gravity
        glm::vec3 gravityForce = glm::vec3(0.0f, -9.81f * carRigidBody.properties.mass, 0.0f);
        physicsEngine.applyForceToRigidBody(carRigidBody, gravityForce);

        // Apply a constant forward force (engine force) for demonstration
        glm::vec3 forwardForce = carTransform.rotation * glm::vec3(0.0f, 0.0f, 10000.0f); // 10000N forward
        physicsEngine.applyForceToRigidBody(carRigidBody, forwardForce);

        // Calculate tire forces (simplified for now)
        // This function will apply forces directly to the rigid body's accumulators
        physicsEngine.calculateTireForces(carTire, carRigidBody, carTransform);

        // Integrate the rigid body state using RK4
        carRigidBody.state = Physics::Integrator::integrateRK4(
            carRigidBody.state,
            carRigidBody.properties,
            carRigidBody.accumulators,
            deltaTime,
            Physics::PhysicsEngine::calculateRigidBodyDerivative
        );

        // Update the transform component from the rigid body state
        carTransform.position = carRigidBody.state.position;
        carTransform.rotation = carRigidBody.state.orientation;

        // Advance simulation time
        simulationTime += deltaTime;

        // Print current state (simplified output)
        std::cout << "Time: " << simulationTime << "s, Position: " << glm::to_string(carTransform.position) 
                  << ", Linear Velocity: " << glm::to_string(carRigidBody.state.linearVelocity) << std::endl;
    }

    std::cout << "\nSimulation finished." << std::endl;

    return 0;
}


