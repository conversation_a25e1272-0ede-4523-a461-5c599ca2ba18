#pragma once

#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>
#include <vector>
#include <memory>

namespace Graphics {

// Light types for different lighting scenarios
enum class LightType {
    DIRECTIONAL,    // Sun/Moon light
    POINT,          // Car headlights, street lamps
    SPOT,           // Focused beams, flashlights
    AREA,           // Large light sources
    IBL             // Image-based lighting for environment
};

// Physical light properties based on real-world measurements
struct LightProperties {
    glm::vec3 color = glm::vec3(1.0f);          // RGB color
    float intensity = 1.0f;                      // Luminous intensity (candela)
    float temperature = 6500.0f;                 // Color temperature (Kelvin)
    float luminousFlux = 1000.0f;               // Total light output (lumens)

    // Attenuation parameters (physically-based)
    float constantAttenuation = 1.0f;
    float linearAttenuation = 0.09f;
    float quadraticAttenuation = 0.032f;

    // For spot lights
    float innerConeAngle = 30.0f;               // Inner cone angle (degrees)
    float outerConeAngle = 45.0f;               // Outer cone angle (degrees)

    // Shadow properties
    bool castsShadows = true;
    float shadowBias = 0.005f;
    int shadowMapSize = 1024;
};

// Base light class
class Light {
public:
    Light(LightType type, const LightProperties& properties);
    virtual ~Light() = default;

    // Getters/Setters
    LightType getType() const { return m_type; }
    const LightProperties& getProperties() const { return m_properties; }
    void setProperties(const LightProperties& properties) { m_properties = properties; }

    glm::vec3 getPosition() const { return m_position; }
    void setPosition(const glm::vec3& position) { m_position = position; }

    glm::vec3 getDirection() const { return m_direction; }
    void setDirection(const glm::vec3& direction) { m_direction = glm::normalize(direction); }

    bool isEnabled() const { return m_enabled; }
    void setEnabled(bool enabled) { m_enabled = enabled; }

    // Light calculations
    virtual glm::vec3 calculateIllumination(const glm::vec3& worldPos, const glm::vec3& normal) const = 0;
    virtual float calculateAttenuation(const glm::vec3& worldPos) const;

    // Color temperature to RGB conversion
    static glm::vec3 temperatureToRGB(float temperature);

    // Physically-based light units conversion
    static float lumensToIntensity(float lumens, LightType type);
    static float candlelaToIntensity(float candela);

protected:
    LightType m_type;
    LightProperties m_properties;
    glm::vec3 m_position = glm::vec3(0.0f);
    glm::vec3 m_direction = glm::vec3(0.0f, -1.0f, 0.0f);
    bool m_enabled = true;
};

// Directional light (sun/moon)
class DirectionalLight : public Light {
public:
    DirectionalLight(const LightProperties& properties);

    glm::vec3 calculateIllumination(const glm::vec3& worldPos, const glm::vec3& normal) const override;

    // Sun/moon specific properties
    void setSunPosition(float azimuth, float elevation);
    void setTimeOfDay(float hours); // 0-24 hours

    // Atmospheric scattering
    glm::vec3 calculateAtmosphericColor(float elevation) const;

private:
    float m_azimuth = 0.0f;     // Sun azimuth angle
    float m_elevation = 45.0f;  // Sun elevation angle
};

// Point light (car headlights, street lamps)
class PointLight : public Light {
public:
    PointLight(const LightProperties& properties);

    glm::vec3 calculateIllumination(const glm::vec3& worldPos, const glm::vec3& normal) const override;
    float calculateAttenuation(const glm::vec3& worldPos) const override;

    // Car headlight specific
    void configureAsHeadlight(float brightness = 3000.0f); // lumens
    void configureAsStreetLight(float brightness = 8000.0f); // lumens
};

// Spot light (focused beams)
class SpotLight : public Light {
public:
    SpotLight(const LightProperties& properties);

    glm::vec3 calculateIllumination(const glm::vec3& worldPos, const glm::vec3& normal) const override;
    float calculateAttenuation(const glm::vec3& worldPos) const override;

    // Spot light specific calculations
    float calculateSpotFactor(const glm::vec3& worldPos) const;

    // Car-specific configurations
    void configureAsHeadlightBeam(float beamWidth = 30.0f);
    void configureAsFogLight(float beamWidth = 60.0f);
};

// Area light (large light sources)
class AreaLight : public Light {
public:
    AreaLight(const LightProperties& properties, const glm::vec2& size);

    glm::vec3 calculateIllumination(const glm::vec3& worldPos, const glm::vec3& normal) const override;

    // Area light specific
    glm::vec2 getSize() const { return m_size; }
    void setSize(const glm::vec2& size) { m_size = size; }

    // Soft shadow calculations
    float calculateSoftShadowFactor(const glm::vec3& worldPos) const;

private:
    glm::vec2 m_size; // Width and height of the area light
};

} // namespace Graphics