#ifndef ECS_COMPONENT_H
#define ECS_COMPONENT_H

#include <cstdint>

namespace ECS {

// Base class for all components. Components are pure data structures.
// This class primarily serves as a tag and to enforce common properties if needed.
class IComponent {
public:
    // A unique ID for each component type. This will be assigned automatically.
    static uint32_t s_componentTypeCounter;
};

// Template to assign a unique ID to each component type at compile time.
template <typename T>
class Component : public IComponent {
public:
    static uint32_t getComponentTypeID() {
        static uint32_t typeID = s_componentTypeCounter++;
        return typeID;
    }
};

} // namespace ECS

#endif // ECS_COMPONENT_H