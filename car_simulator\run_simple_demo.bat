@echo off
echo === Simple Car Simulator Demo ===
echo.

echo Compiling simple demo (no external dependencies)...

REM Try different compilers
where g++ >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Using g++ compiler...
    g++ -std=c++17 -O2 -o simple_demo.exe simple_demo.cpp
    goto :run_demo
)

where clang++ >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Using clang++ compiler...
    clang++ -std=c++17 -O2 -o simple_demo.exe simple_demo.cpp
    goto :run_demo
)

where cl >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Using MSVC compiler...
    cl /std:c++17 /O2 /EHsc simple_demo.cpp /Fe:simple_demo.exe
    goto :run_demo
)

echo ERROR: No C++ compiler found!
echo Please install one of the following:
echo - MinGW (g++)
echo - LLVM (clang++)
echo - Visual Studio (MSVC)
echo.
pause
exit /b 1

:run_demo
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ERROR: Compilation failed.
    pause
    exit /b 1
)

echo.
echo Compilation successful! Running demo...
echo.

simple_demo.exe

echo.
echo Demo completed. Press any key to exit.
pause >nul
