#include "physics_engine.h"
#include "../ecs/component.h"
#include <algorithm>
#include <cmath>

namespace Physics {

PhysicsEngine::PhysicsEngine() {
    // Initialize physics engine
    m_gravity = glm::vec3(0.0f, GRAVITY, 0.0f);
    m_airDensity = AIR_DENSITY;
}

PhysicsEngine::~PhysicsEngine() {
    m_rigidBodies.clear();
    m_forceAccumulators.clear();
}

void PhysicsEngine::update(float deltaTime) {
    // Clear force accumulators from previous frame
    clearForceAccumulators();

    // Apply gravity to all rigid bodies
    for (auto& body : m_rigidBodies) {
        if (body) {
            glm::vec3 gravityForce = m_gravity * body->getMass();
            applyForce(body, gravityForce);
        }
    }

    // Handle collisions
    handleCollisions();

    // Integrate all rigid bodies
    integrateRigidBodies(deltaTime);
}

// Rigid body management
void PhysicsEngine::addRigidBody(std::shared_ptr<RigidBody> body) {
    if (body) {
        m_rigidBodies.push_back(body);
        m_forceAccumulators[body] = ForceAccumulator();
    }
}

void PhysicsEngine::removeRigidBody(std::shared_ptr<RigidBody> body) {
    auto it = std::find(m_rigidBodies.begin(), m_rigidBodies.end(), body);
    if (it != m_rigidBodies.end()) {
        m_rigidBodies.erase(it);
        m_forceAccumulators.erase(body);
    }
}

// Force application
void PhysicsEngine::applyForce(std::shared_ptr<RigidBody> body, const glm::vec3& force) {
    auto it = m_forceAccumulators.find(body);
    if (it != m_forceAccumulators.end()) {
        it->second.addForce(force);
    }
}

void PhysicsEngine::applyForceAtPoint(std::shared_ptr<RigidBody> body, const glm::vec3& force, const glm::vec3& point) {
    auto it = m_forceAccumulators.find(body);
    if (it != m_forceAccumulators.end()) {
        it->second.addForceAtPoint(force, point, body->getPosition());
    }
}

void PhysicsEngine::applyTorque(std::shared_ptr<RigidBody> body, const glm::vec3& torque) {
    auto it = m_forceAccumulators.find(body);
    if (it != m_forceAccumulators.end()) {
        it->second.addTorque(torque);
    }
}

RigidBodyDerivative PhysicsEngine::calculateRigidBodyDerivative(
    const RigidBodyState& state,
    const RigidBodyProperties& properties,
    const RigidBodyForceAccumulators& accumulators
) {
    RigidBodyDerivative derivative;

    // Linear velocity derivative (acceleration)
    derivative.dLinearVelocity = accumulators.force / properties.mass;

    // Angular velocity derivative (angular acceleration)
    // Convert angular velocity to world space for inertia tensor multiplication
    glm::mat3 inertiaTensorWorld = glm::mat3(state.orientation) * properties.inertiaTensor * glm::transpose(glm::mat3(state.orientation));
    glm::mat3 invInertiaTensorWorld = glm::mat3(state.orientation) * properties.invInertiaTensor * glm::transpose(glm::mat3(state.orientation));

    // Euler's equation for rigid body rotation: I * alpha = torque - (omega x (I * omega))
    // alpha = inv(I) * (torque - (omega x (I * omega)))
    glm::vec3 Iw = inertiaTensorWorld * state.angularVelocity;
    glm::vec3 omega_cross_Iw = glm::cross(state.angularVelocity, Iw);
    derivative.dAngularVelocity = invInertiaTensorWorld * (accumulators.torque - omega_cross_Iw);

    // Position derivative (linear velocity)
    derivative.dPosition = state.linearVelocity;

    // Orientation derivative (angular velocity represented as quaternion derivative)
    // dq/dt = 0.5 * q * omega_quat
    glm::quat angularVelocityQuat = glm::quat(0.0f, state.angularVelocity.x, state.angularVelocity.y, state.angularVelocity.z);
    derivative.dOrientation = 0.5f * state.orientation * angularVelocityQuat;

    return derivative;
}

// Tire force calculations using Pacejka Magic Formula
glm::vec3 PhysicsEngine::calculateTireForce(const TireParameters& tire, float slipRatio, float slipAngle, float normalForce) {
    glm::vec3 longitudinalForce = calculateLongitudinalForce(tire, slipRatio, normalForce);
    glm::vec3 lateralForce = calculateLateralForce(tire, slipAngle, normalForce);

    return longitudinalForce + lateralForce;
}

glm::vec3 PhysicsEngine::calculateLongitudinalForce(const TireParameters& tire, float slipRatio, float normalForce) {
    float Fx = pacejkaFormula(slipRatio, tire.longitudinalStiffness, tire.shapeFactorB,
                             tire.peakValue * normalForce, tire.curvature,
                             tire.horizontalShift, tire.verticalShift);
    return glm::vec3(Fx, 0.0f, 0.0f); // Longitudinal force along X-axis
}

glm::vec3 PhysicsEngine::calculateLateralForce(const TireParameters& tire, float slipAngle, float normalForce) {
    float Fy = pacejkaFormula(slipAngle, tire.longitudinalStiffness, tire.shapeFactorB,
                             tire.peakValue * normalForce, tire.curvature,
                             tire.horizontalShift, tire.verticalShift);
    return glm::vec3(0.0f, 0.0f, Fy); // Lateral force along Z-axis
}

// Aerodynamic forces
glm::vec3 PhysicsEngine::calculateDragForce(const glm::vec3& velocity, const VehicleParameters& vehicle) {
    float speed = glm::length(velocity);
    if (speed < 0.01f) return glm::vec3(0.0f); // Avoid division by zero

    glm::vec3 velocityDirection = glm::normalize(velocity);
    float dragMagnitude = 0.5f * m_airDensity * vehicle.dragCoefficient * vehicle.frontalArea * speed * speed;

    return -dragMagnitude * velocityDirection; // Drag opposes motion
}

glm::vec3 PhysicsEngine::calculateLiftForce(const glm::vec3& velocity, const VehicleParameters& vehicle) {
    float speed = glm::length(velocity);
    if (speed < 0.01f) return glm::vec3(0.0f);

    float liftMagnitude = 0.5f * m_airDensity * vehicle.liftCoefficient * vehicle.frontalArea * speed * speed;

    return glm::vec3(0.0f, -liftMagnitude, 0.0f); // Lift reduces normal force (negative Y)
}

// Suspension forces
glm::vec3 PhysicsEngine::calculateSuspensionForce(float compression, float compressionRate,
                                                 float springConstant, float dampingConstant) {
    // F = -kx - cv (spring force + damping force)
    float springForce = -springConstant * compression;
    float dampingForce = -dampingConstant * compressionRate;

    return glm::vec3(0.0f, springForce + dampingForce, 0.0f); // Vertical force
}

// Collision detection and response
void PhysicsEngine::handleCollisions() {
    // Placeholder for collision detection and response
    // In a real implementation, this would use a collision detection library
    // like Bullet Physics or custom broad/narrow phase detection

    // For now, implement simple ground collision
    for (auto& body : m_rigidBodies) {
        if (body && body->getPosition().y < 0.0f) {
            // Simple ground collision response
            glm::vec3 position = body->getPosition();
            position.y = 0.0f;
            body->setPosition(position);

            // Apply restitution to velocity
            glm::vec3 velocity = body->getLinearVelocity();
            if (velocity.y < 0.0f) {
                velocity.y *= -0.5f; // 50% restitution
                body->setLinearVelocity(velocity);
            }
        }
    }
}

// ECS integration methods
void PhysicsEngine::applyForceToRigidBody(
    ECS::RigidBodyComponent& rbComponent,
    const glm::vec3& force,
    const glm::vec3& relativePoint
) {
    rbComponent.accumulators.force += force;
    rbComponent.accumulators.torque += glm::cross(relativePoint, force);
}

void PhysicsEngine::calculateTireForces(
    ECS::TireComponent& tireComponent,
    ECS::RigidBodyComponent& rbComponent,
    ECS::TransformComponent& transformComponent
) {
    // Calculate slip ratio and slip angle based on vehicle dynamics
    // This is a simplified implementation

    // Assume some basic vehicle parameters
    float wheelRadius = 0.3f;
    float normalLoad = 5000.0f; // N

    // Calculate slip ratio (simplified)
    glm::vec3 wheelVelocity = rbComponent.state.linearVelocity;
    float wheelSpeed = glm::length(wheelVelocity);
    float slipRatio = 0.0f; // Simplified - would need wheel angular velocity

    // Calculate slip angle (simplified)
    float slipAngle = glm::radians(2.0f); // Simplified - would need steering input

    // Calculate tire forces using Pacejka model
    TireParameters tireParams = tireComponent.params;
    glm::vec3 tireForce = calculateTireForce(tireParams, slipRatio, slipAngle, normalLoad);

    // Transform force to world coordinates
    glm::vec3 worldForce = transformComponent.rotation * tireForce;

    // Apply force at tire contact point
    glm::vec3 tirePosition = tireComponent.relativePosition;
    applyForceToRigidBody(rbComponent, worldForce, tirePosition);

    // Store calculated force for debugging
    tireComponent.tireForce = worldForce;
}

// Internal helper functions
float PhysicsEngine::pacejkaFormula(float input, float B, float C, float D, float E, float Sh, float Sv) {
    // Pacejka Magic Formula: y = D * sin(C * atan(B * x - E * (B * x - atan(B * x))))
    // where x = input + Sh, y = output + Sv

    float x = input + Sh;
    float Bx = B * x;
    float atanBx = std::atan(Bx);
    float y = D * std::sin(C * std::atan(Bx - E * (Bx - atanBx)));

    return y + Sv;
}

void PhysicsEngine::integrateRigidBodies(float deltaTime) {
    for (auto& body : m_rigidBodies) {
        if (body) {
            auto it = m_forceAccumulators.find(body);
            if (it != m_forceAccumulators.end()) {
                // Apply accumulated forces to rigid body
                body->integrate(it->second.force, it->second.torque, deltaTime);
            }
        }
    }
}

void PhysicsEngine::clearForceAccumulators() {
    for (auto& pair : m_forceAccumulators) {
        pair.second.clear();
    }
}

} // namespace Physics