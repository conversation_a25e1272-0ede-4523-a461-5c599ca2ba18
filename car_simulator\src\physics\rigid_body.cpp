#include "rigid_body.h"
#include <algorithm>
#include <cmath>

namespace Physics {

RigidBody::RigidBody(float mass, const glm::mat3& inertiaTensor)
    : m_properties(mass, inertiaTensor) {
    m_inverseMass = (mass > 0.0f) ? 1.0f / mass : 0.0f;

    // Initialize state
    m_state.position = glm::vec3(0.0f);
    m_state.orientation = glm::quat(1.0f, 0.0f, 0.0f, 0.0f); // Identity quaternion
    m_state.linearVelocity = glm::vec3(0.0f);
    m_state.angularVelocity = glm::vec3(0.0f);

    // Clear accumulators
    m_accumulators.clear();
}

RigidBody::RigidBody(const RigidBodyProperties& properties)
    : m_properties(properties) {
    m_inverseMass = (properties.mass > 0.0f) ? 1.0f / properties.mass : 0.0f;

    // Initialize state
    m_state.position = glm::vec3(0.0f);
    m_state.orientation = glm::quat(1.0f, 0.0f, 0.0f, 0.0f);
    m_state.linearVelocity = glm::vec3(0.0f);
    m_state.angularVelocity = glm::vec3(0.0f);

    // Clear accumulators
    m_accumulators.clear();
}

RigidBody::~RigidBody() {
    // Destructor
}

void RigidBody::setMass(float mass) {
    m_properties.mass = mass;
    m_inverseMass = (mass > 0.0f) ? 1.0f / mass : 0.0f;
}

glm::mat3 RigidBody::getWorldInertiaTensor() const {
    glm::mat3 rotationMatrix = glm::mat3_cast(m_state.orientation);
    return rotationMatrix * m_properties.inertiaTensor * glm::transpose(rotationMatrix);
}

glm::mat3 RigidBody::getWorldInverseInertiaTensor() const {
    glm::mat3 rotationMatrix = glm::mat3_cast(m_state.orientation);
    return rotationMatrix * m_properties.invInertiaTensor * glm::transpose(rotationMatrix);
}

void RigidBody::applyForce(const glm::vec3& force) {
    m_accumulators.force += force;
    setAwake(true);
}

void RigidBody::applyForceAtPoint(const glm::vec3& force, const glm::vec3& worldPoint) {
    m_accumulators.force += force;

    // Calculate torque: τ = r × F
    glm::vec3 relativePoint = worldPoint - m_state.position;
    m_accumulators.torque += glm::cross(relativePoint, force);

    setAwake(true);
}

void RigidBody::applyTorque(const glm::vec3& torque) {
    m_accumulators.torque += torque;
    setAwake(true);
}

void RigidBody::applyImpulse(const glm::vec3& impulse) {
    m_state.linearVelocity += impulse * m_inverseMass;
    setAwake(true);
}

void RigidBody::applyImpulseAtPoint(const glm::vec3& impulse, const glm::vec3& worldPoint) {
    // Apply linear impulse
    m_state.linearVelocity += impulse * m_inverseMass;

    // Apply angular impulse
    glm::vec3 relativePoint = worldPoint - m_state.position;
    glm::vec3 angularImpulse = glm::cross(relativePoint, impulse);
    glm::mat3 worldInverseInertia = getWorldInverseInertiaTensor();
    m_state.angularVelocity += worldInverseInertia * angularImpulse;

    setAwake(true);
}

void RigidBody::integrate(const glm::vec3& totalForce, const glm::vec3& totalTorque, float deltaTime) {
    if (!m_isAwake || deltaTime <= 0.0f) return;

    // Linear integration
    glm::vec3 acceleration = totalForce * m_inverseMass;
    m_state.linearVelocity += acceleration * deltaTime;
    m_state.position += m_state.linearVelocity * deltaTime;

    // Angular integration
    glm::mat3 worldInverseInertia = getWorldInverseInertiaTensor();
    glm::vec3 angularAcceleration = worldInverseInertia * totalTorque;
    m_state.angularVelocity += angularAcceleration * deltaTime;

    // Update orientation using quaternion integration
    if (glm::length(m_state.angularVelocity) > 0.0f) {
        glm::quat angularVelocityQuat(0.0f, m_state.angularVelocity.x, m_state.angularVelocity.y, m_state.angularVelocity.z);
        glm::quat orientationDerivative = 0.5f * m_state.orientation * angularVelocityQuat;
        m_state.orientation += orientationDerivative * deltaTime;
        m_state.orientation = glm::normalize(m_state.orientation);
    }

    // Apply damping to prevent numerical instability
    const float linearDamping = 0.99f;
    const float angularDamping = 0.99f;
    m_state.linearVelocity *= linearDamping;
    m_state.angularVelocity *= angularDamping;

    // Update sleep state
    updateSleepState(deltaTime);

    // Clear accumulators for next frame
    clearAccumulators();
}

void RigidBody::clearAccumulators() {
    m_accumulators.clear();
}

glm::vec3 RigidBody::getPointVelocity(const glm::vec3& worldPoint) const {
    glm::vec3 relativePoint = worldPoint - m_state.position;
    return m_state.linearVelocity + glm::cross(m_state.angularVelocity, relativePoint);
}

glm::vec3 RigidBody::worldToLocal(const glm::vec3& worldPoint) const {
    glm::mat3 rotationMatrix = glm::transpose(glm::mat3_cast(m_state.orientation));
    return rotationMatrix * (worldPoint - m_state.position);
}

glm::vec3 RigidBody::localToWorld(const glm::vec3& localPoint) const {
    glm::mat3 rotationMatrix = glm::mat3_cast(m_state.orientation);
    return m_state.position + rotationMatrix * localPoint;
}

float RigidBody::getKineticEnergy() const {
    return 0.5f * m_properties.mass * glm::dot(m_state.linearVelocity, m_state.linearVelocity);
}

float RigidBody::getRotationalEnergy() const {
    glm::mat3 worldInertia = getWorldInertiaTensor();
    glm::vec3 Iw = worldInertia * m_state.angularVelocity;
    return 0.5f * glm::dot(m_state.angularVelocity, Iw);
}

float RigidBody::getTotalEnergy() const {
    return getKineticEnergy() + getRotationalEnergy();
}

bool RigidBody::canSleep() const {
    float linearSpeed = glm::length(m_state.linearVelocity);
    float angularSpeed = glm::length(m_state.angularVelocity);

    return (linearSpeed < SLEEP_THRESHOLD && angularSpeed < SLEEP_THRESHOLD);
}

void RigidBody::updateSleepState(float deltaTime) {
    if (canSleep()) {
        m_sleepTimer += deltaTime;
        if (m_sleepTimer >= SLEEP_TIME) {
            setAwake(false);
            m_state.linearVelocity = glm::vec3(0.0f);
            m_state.angularVelocity = glm::vec3(0.0f);
        }
    } else {
        m_sleepTimer = 0.0f;
        setAwake(true);
    }
}

void RigidBody::updateInertiaTensor() {
    m_properties.invInertiaTensor = glm::inverse(m_properties.inertiaTensor);
}

} // namespace Physics