#ifndef GRAPHICS_ENGINE_H
#define GRAPHICS_ENGINE_H

#include <vector>
#include <memory>
#include <string>

#include <glm/glm.hpp>

// Forward declarations for ECS components
namespace ECS {
    struct TransformComponent;
    struct RenderMeshComponent;
}

namespace Graphics {

class GraphicsEngine {
public:
    GraphicsEngine();
    ~GraphicsEngine();

    /**
     * @brief Initializes the graphics engine (Vulkan/DirectX 12).
     * @param windowHandle Handle to the rendering window.
     * @param width Width of the rendering surface.
     * @param height Height of the rendering surface.
     * @return True if initialization is successful, false otherwise.
     */
    bool initialize(void* windowHandle, int width, int height);

    /**
     * @brief Renders a single frame.
     * @param deltaTime Time elapsed since the last frame.
     */
    void render(float deltaTime);

    /**
     * @brief Resizes the rendering surface.
     * @param width New width.
     * @param height New height.
     */
    void resize(int width, int height);

    // Placeholder for PBR rendering
    void setupPBRMaterials();

    // Placeholder for Ray Tracing
    void setupRayTracing();

    // Placeholder for HDR and Tone Mapping
    void setupHDRToneMapping();

    // TODO: Integrate with ECS system
    // For now, assume we have direct access to components for rendering.
    // In a full ECS, a RenderSystem would iterate over relevant components.
    void addRenderableEntity(ECS::TransformComponent* transform, ECS::RenderMeshComponent* mesh);

private:
    // Internal rendering API (Vulkan/DirectX 12) specific objects
    // e.g., VkInstance, VkDevice, ID3D12Device, etc.
    void* m_graphicsAPIContext; 

    // List of renderable entities (simplified for now)
    struct RenderableEntity {
        ECS::TransformComponent* transform;
        ECS::RenderMeshComponent* mesh;
    };
    std::vector<RenderableEntity> m_renderableEntities;

    // Other graphics-related members (e.g., camera, lights, post-processing effects)
};

} // namespace Graphics

#endif // GRAPHICS_ENGINE_H