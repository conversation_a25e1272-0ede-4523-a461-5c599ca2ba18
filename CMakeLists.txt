cmake_minimum_required(VERSION 3.10)

project(CarSimulator VERSION 1.0.0)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED TRUE)

# Find GLM
find_package(glm CONFIG REQUIRED)

# Add source directories
include_directories(src)
include_directories(src/physics)
include_directories(src/ecs)

# Add executable
add_executable(CarSimulator src/main.cpp src/physics/PhysicsEngine.cpp)

# Link libraries
# GLM is header-only, so no explicit linking is usually needed, but good practice to include.
# If using a compiled version of GLM, you might need target_link_libraries(CarSimulator glm::glm)

# For now, we are not linking Bullet Physics or NVIDIA PhysX, FMOD, Wwise, Boost.Asio
# These will be added later when their integration is required.

# Example of how to link if they were found:
# find_package(Bullet REQUIRED)
# target_link_libraries(CarSimulator Bullet::BulletDynamics Bullet::BulletCollision Bullet::LinearMath)

# find_package(Boost COMPONENTS system REQUIRED)
# target_link_libraries(CarSimulator Boost::system)