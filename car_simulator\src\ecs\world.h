#ifndef ECS_WORLD_H
#define ECS_WORLD_H

#include "Entity.h"
#include "Component.h"
#include "System.h"
#include "Components.h" // Include all defined components

#include <vector>
#include <array>
#include <memory>
#include <unordered_map>
#include <algorithm>
#include <bitset>

namespace ECS {

const uint32_t MAX_COMPONENTS = 32; // Maximum number of unique component types
const uint32_t MAX_ENTITIES = 5000; // Maximum number of entities

// A bitset representing the components an entity has
using ComponentSignature = std::bitset<MAX_COMPONENTS>;

class World {
public:
    World();
    ~World();

    // --- Entity Management ---
    Entity createEntity();
    void destroyEntity(Entity entity);

    // --- Component Management ---
    template <typename T, typename... Args>
    T& addComponent(Entity entity, Args&&... args);

    template <typename T>
    void removeComponent(Entity entity);

    template <typename T>
    T& getComponent(Entity entity);

    template <typename T>
    bool hasComponent(Entity entity) const;

    // --- System Management ---
    template <typename T, typename... Args>
    T& addSystem(Args&&... args);

    template <typename T>
    void removeSystem();

    template <typename T>
    T& getSystem();

    // --- Update Loop ---
    void update(float deltaTime);

private:
    // Entity data
    std::vector<Entity> m_availableEntities; // Pool of available entity IDs
    uint32_t m_nextEntityId;                 // Next entity ID to assign
    std::vector<ComponentSignature> m_entitySignatures; // Component signature for each entity

    // Component data (array of vectors, one vector per component type)
    std::array<std::vector<uint8_t>, MAX_COMPONENTS> m_componentPools; // Raw storage for components
    std::array<size_t, MAX_COMPONENTS> m_componentSizes; // Size of each component type
    std::array<uint32_t, MAX_COMPONENTS> m_componentNextIndex; // Next available index in component pool

    // Map from entity ID to component index within its pool
    std::vector<std::unordered_map<uint32_t, uint32_t>> m_entityComponentIndices; // entityId -> componentTypeID -> indexInPool

    // System data
    std::vector<std::unique_ptr<ISystem>> m_systems; // All registered systems
    std::unordered_map<std::type_index, ISystem*> m_systemMap; // Type index to system pointer

    // Helper to get component type ID
    template <typename T>
    uint32_t getComponentTypeID() const {
        return Component<T>::getComponentTypeID();
    }

    // Helper to update system entity lists based on component signatures
    void updateSystemEntityLists(Entity entity, const ComponentSignature& newSignature);
};

// --- Template Implementations ---

template <typename T, typename... Args>
T& World::addComponent(Entity entity, Args&&... args) {
    uint32_t componentTypeID = getComponentTypeID<T>();
    if (componentTypeID >= MAX_COMPONENTS) {
        throw std::runtime_error("Exceeded max component types.");
    }

    // Ensure component pool has enough space
    if (m_componentPools[componentTypeID].empty()) {
        m_componentPools[componentTypeID].resize(MAX_ENTITIES * sizeof(T));
        m_componentSizes[componentTypeID] = sizeof(T);
        m_componentNextIndex[componentTypeID] = 0;
    }

    // Get next available index in the component pool
    uint32_t componentIndex = m_componentNextIndex[componentTypeID]++;
    if (componentIndex >= MAX_ENTITIES) {
        throw std::runtime_error("Exceeded max components of this type.");
    }

    // Construct the component directly in the pre-allocated memory
    T* componentPtr = reinterpret_cast<T*>(
        m_componentPools[componentTypeID].data() + (componentIndex * sizeof(T))
    );
    new (componentPtr) T(std::forward<Args>(args)...); // Placement new

    // Store the index of the component for this entity
    m_entityComponentIndices[entity][componentTypeID] = componentIndex;

    // Update entity signature
    m_entitySignatures[entity].set(componentTypeID);

    // Update systems that are interested in this entity
    updateSystemEntityLists(entity, m_entitySignatures[entity]);

    return *componentPtr;
}

template <typename T>
void World::removeComponent(Entity entity) {
    uint32_t componentTypeID = getComponentTypeID<T>();
    if (!hasComponent<T>(entity)) {
        throw std::runtime_error("Entity does not have this component.");
    }

    // Get the index of the component to remove
    uint32_t componentIndexToRemove = m_entityComponentIndices[entity][componentTypeID];

    // Get the last component in the pool for this type
    uint32_t lastComponentIndex = m_componentNextIndex[componentTypeID] - 1;

    // If the component to remove is not the last one, move the last one to its place
    if (componentIndexToRemove != lastComponentIndex) {
        // Find the entity that owned the last component
        Entity entityOfLastComponent = INVALID_ENTITY;
        for (const auto& pair : m_entityComponentIndices[lastComponentIndex]) {
            if (pair.second == lastComponentIndex && pair.first != entity) { // Check if it's the entity we're looking for and not the one we're removing
                entityOfLastComponent = pair.first;
                break;
            }
        }

        if (entityOfLastComponent != INVALID_ENTITY) {
            // Move the data
            std::memcpy(
                m_componentPools[componentTypeID].data() + (componentIndexToRemove * sizeof(T)),
                m_componentPools[componentTypeID].data() + (lastComponentIndex * sizeof(T)),
                sizeof(T)
            );
            // Update the index for the entity that owned the moved component
            m_entityComponentIndices[entityOfLastComponent][componentTypeID] = componentIndexToRemove;
        }
    }

    // Decrement the count of components of this type
    m_componentNextIndex[componentTypeID]--;

    // Remove the component index from the entity mapping
    m_entityComponentIndices[entity].erase(componentTypeID);

    // Update entity signature
    m_entitySignatures[entity].reset(componentTypeID);

    // Update systems that are interested in this entity
    updateSystemEntityLists(entity, m_entitySignatures[entity]);
}

template <typename T>
T& World::getComponent(Entity entity) {
    uint32_t componentTypeID = getComponentTypeID<T>();
    if (!hasComponent<T>(entity)) {
        throw std::runtime_error("Entity does not have this component.");
    }
    uint32_t componentIndex = m_entityComponentIndices[entity][componentTypeID];
    return *reinterpret_cast<T*>(
        m_componentPools[componentTypeID].data() + (componentIndex * sizeof(T))
    );
}

template <typename T>
bool World::hasComponent(Entity entity) const {
    uint32_t componentTypeID = getComponentTypeID<T>();
    return m_entitySignatures[entity].test(componentTypeID);
}

template <typename T, typename... Args>
T& World::addSystem(Args&&... args) {
    static_assert(std::is_base_of<ISystem, T>::value, "T must inherit from ISystem");
    std::unique_ptr<T> newSystem = std::make_unique<T>(std::forward<Args>(args)...);
    T* systemPtr = newSystem.get();
    m_systems.push_back(std::move(newSystem));
    m_systemMap[typeid(T)] = systemPtr;

    // Check existing entities against the new system's signature
    for (Entity entity = 0; entity < m_nextEntityId; ++entity) {
        if (m_entitySignatures[entity].any()) { // Only check active entities
            updateSystemEntityLists(entity, m_entitySignatures[entity]);
        }
    }
    return *systemPtr;
}

template <typename T>
void World::removeSystem() {
    static_assert(std::is_base_of<ISystem, T>::value, "T must inherit from ISystem");
    std::type_index typeIndex = typeid(T);
    auto it = std::find_if(m_systems.begin(), m_systems.end(), 
        [&](const std::unique_ptr<ISystem>& sys){ return typeid(*sys) == typeIndex; });

    if (it != m_systems.end()) {
        m_systemMap.erase(typeIndex);
        // Remove entities from this system's list before deleting
        (*it)->m_entities.clear(); 
        m_systems.erase(it);
    } else {
        throw std::runtime_error("System not found.");
    }
}

template <typename T>
T& World::getSystem() {
    static_assert(std::is_base_of<ISystem, T>::value, "T must inherit from ISystem");
    auto it = m_systemMap.find(typeid(T));
    if (it == m_systemMap.end()) {
        throw std::runtime_error("System not found.");
    }
    return *static_cast<T*>(it->second);
}

} // namespace ECS

#endif // ECS_WORLD_H