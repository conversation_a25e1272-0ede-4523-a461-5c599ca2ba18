using UnityEngine;

public class CameraFollow : MonoBehaviour
{
    [Header("إعدادات الكاميرا")]
    public Transform target;                    // السيارة المستهدفة
    public float distance = 10.0f;             // المسافة من السيارة
    public float height = 5.0f;                // الارتفاع فوق السيارة
    public float rotationDamping = 3.0f;       // سلاسة دوران الكاميرا
    public float heightDamping = 2.0f;         // سلاسة حركة الارتفاع
    
    [Header("أوضاع الكاميرا")]
    public CameraMode currentMode = CameraMode.FollowBehind;
    
    [Header("إعدادات متقدمة")]
    public bool lookAtTarget = true;           // النظر نحو السيارة
    public float fieldOfView = 60f;            // زاوية الرؤية
    public LayerMask obstacleLayerMask = -1;   // طبقات العوائق
    
    // أوضاع الكاميرا المختلفة
    public enum CameraMode
    {
        FollowBehind,    // خلف السيارة
        FirstPerson,     // منظور الشخص الأول
        TopDown,         // من الأعلى
        Cinematic        // سينمائي
    }
    
    // متغيرات داخلية
    private Camera cam;
    private Vector3 velocity = Vector3.zero;
    private float currentDistance;
    private float currentHeight;
    
    void Start()
    {
        // الحصول على مكون الكاميرا
        cam = GetComponent<Camera>();
        
        // تعيين زاوية الرؤية
        cam.fieldOfView = fieldOfView;
        
        // البحث عن السيارة إذا لم يتم تحديدها
        if (target == null)
        {
            GameObject car = GameObject.FindGameObjectWithTag("Player");
            if (car != null)
            {
                target = car.transform;
            }
        }
        
        // تهيئة المتغيرات
        currentDistance = distance;
        currentHeight = height;
        
        Debug.Log("📷 تم تهيئة كاميرا السيارة");
    }
    
    void LateUpdate()
    {
        if (target == null) return;
        
        // التحكم في أوضاع الكاميرا
        HandleCameraModeInput();
        
        // تحديث موضع الكاميرا حسب الوضع
        switch (currentMode)
        {
            case CameraMode.FollowBehind:
                FollowBehindMode();
                break;
            case CameraMode.FirstPerson:
                FirstPersonMode();
                break;
            case CameraMode.TopDown:
                TopDownMode();
                break;
            case CameraMode.Cinematic:
                CinematicMode();
                break;
        }
    }
    
    void HandleCameraModeInput()
    {
        // تغيير وضع الكاميرا بالضغط على C
        if (Input.GetKeyDown(KeyCode.C))
        {
            CycleCamera();
        }
        
        // تكبير/تصغير بعجلة الماوس
        float scroll = Input.GetAxis("Mouse ScrollWheel");
        if (scroll != 0f)
        {
            distance = Mathf.Clamp(distance - scroll * 2f, 3f, 20f);
        }
    }
    
    void CycleCamera()
    {
        // التنقل بين أوضاع الكاميرا
        int currentIndex = (int)currentMode;
        currentIndex = (currentIndex + 1) % System.Enum.GetValues(typeof(CameraMode)).Length;
        currentMode = (CameraMode)currentIndex;
        
        Debug.Log($"📷 تم تغيير وضع الكاميرا إلى: {currentMode}");
    }
    
    void FollowBehindMode()
    {
        // الموضع المطلوب خلف السيارة
        Vector3 wantedPosition = target.TransformPoint(0, currentHeight, -currentDistance);
        
        // فحص العوائق
        wantedPosition = CheckForObstacles(wantedPosition);
        
        // تحريك الكاميرا بسلاسة
        transform.position = Vector3.SmoothDamp(transform.position, wantedPosition, ref velocity, heightDamping);
        
        // النظر نحو السيارة
        if (lookAtTarget)
        {
            Vector3 lookDirection = target.position - transform.position;
            Quaternion rotation = Quaternion.LookRotation(lookDirection);
            transform.rotation = Quaternion.Slerp(transform.rotation, rotation, rotationDamping * Time.deltaTime);
        }
    }
    
    void FirstPersonMode()
    {
        // موضع داخل السيارة
        Vector3 cockpitPosition = target.TransformPoint(0, 1.2f, 0.5f);
        transform.position = cockpitPosition;
        
        // دوران مع السيارة
        transform.rotation = Quaternion.Slerp(transform.rotation, target.rotation, rotationDamping * Time.deltaTime);
    }
    
    void TopDownMode()
    {
        // موضع من الأعلى
        Vector3 topPosition = target.position + Vector3.up * 15f;
        transform.position = Vector3.SmoothDamp(transform.position, topPosition, ref velocity, heightDamping);
        
        // النظر للأسفل
        transform.LookAt(target.position);
    }
    
    void CinematicMode()
    {
        // وضع سينمائي متحرك
        float time = Time.time;
        Vector3 offset = new Vector3(
            Mathf.Sin(time * 0.5f) * 8f,
            5f + Mathf.Sin(time * 0.3f) * 2f,
            Mathf.Cos(time * 0.5f) * 8f
        );
        
        Vector3 cinematicPosition = target.position + offset;
        transform.position = Vector3.SmoothDamp(transform.position, cinematicPosition, ref velocity, heightDamping);
        
        // النظر نحو السيارة
        transform.LookAt(target.position);
    }
    
    Vector3 CheckForObstacles(Vector3 wantedPosition)
    {
        // فحص وجود عوائق بين الكاميرا والموضع المطلوب
        Vector3 direction = wantedPosition - target.position;
        RaycastHit hit;
        
        if (Physics.Raycast(target.position, direction.normalized, out hit, direction.magnitude, obstacleLayerMask))
        {
            // إذا كان هناك عائق، ضع الكاميرا قبل العائق
            return hit.point - direction.normalized * 0.5f;
        }
        
        return wantedPosition;
    }
    
    // عرض معلومات الكاميرا
    void OnGUI()
    {
        // إعداد النمط
        GUIStyle style = new GUIStyle(GUI.skin.label);
        style.fontSize = 14;
        style.normal.textColor = Color.yellow;
        
        // عرض وضع الكاميرا الحالي
        GUI.Label(new Rect(Screen.width - 200, 10, 180, 20), $"وضع الكاميرا: {GetCameraModeArabic()}", style);
        GUI.Label(new Rect(Screen.width - 200, 30, 180, 20), "اضغط C لتغيير الوضع", style);
        GUI.Label(new Rect(Screen.width - 200, 50, 180, 20), "عجلة الماوس للتكبير/التصغير", style);
    }
    
    string GetCameraModeArabic()
    {
        switch (currentMode)
        {
            case CameraMode.FollowBehind: return "خلف السيارة";
            case CameraMode.FirstPerson: return "منظور أول";
            case CameraMode.TopDown: return "من الأعلى";
            case CameraMode.Cinematic: return "سينمائي";
            default: return "غير محدد";
        }
    }
    
    // رسم خطوط المساعدة في المحرر
    void OnDrawGizmosSelected()
    {
        if (target == null) return;
        
        // رسم خط من الكاميرا إلى السيارة
        Gizmos.color = Color.yellow;
        Gizmos.DrawLine(transform.position, target.position);
        
        // رسم دائرة حول السيارة
        Gizmos.color = Color.green;
        Gizmos.DrawWireSphere(target.position, 2f);
        
        // رسم الموضع المطلوب للكاميرا
        Vector3 wantedPos = target.TransformPoint(0, height, -distance);
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(wantedPos, 0.5f);
    }
}
