@echo off
echo === Car Simulator Build Script ===
echo.

REM Create build directory
if not exist "build" mkdir build
cd build

echo Configuring project with CMake...
cmake .. -G "Visual Studio 17 2022" -A x64

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo CMake configuration failed. Trying with MinGW...
    cmake .. -G "MinGW Makefiles"
)

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo CMake configuration failed. Trying with Ninja...
    cmake .. -G "Ninja"
)

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ERROR: CMake configuration failed with all generators.
    echo Please install Visual Studio 2022, MinGW, or Ninja.
    pause
    exit /b 1
)

echo.
echo Building project...
cmake --build . --config Release

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ERROR: Build failed.
    pause
    exit /b 1
)

echo.
echo === Build completed successfully! ===
echo Executable location: build\bin\CarSimulator.exe
echo.

REM Try to run the simulator
if exist "bin\CarSimulator.exe" (
    echo Running Car Simulator...
    echo.
    bin\CarSimulator.exe
) else if exist "Release\CarSimulator.exe" (
    echo Running Car Simulator...
    echo.
    Release\CarSimulator.exe
) else if exist "CarSimulator.exe" (
    echo Running Car Simulator...
    echo.
    CarSimulator.exe
) else (
    echo Executable not found. Please check the build output.
)

echo.
pause
