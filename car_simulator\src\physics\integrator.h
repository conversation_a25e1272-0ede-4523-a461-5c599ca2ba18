#ifndef INTEGRATOR_H
#define INTEGRATOR_H

#include "RigidBody.h"
#include <functional> // Added for std::function

namespace Physics {

// Function type for calculating derivatives
// Takes current state, accumulated forces/torques, and returns derivatives
using StateDerivativeFunction = std::function<RigidBodyDerivative(const RigidBodyState&, const RigidBodyProperties&, const RigidBodyForceAccumulators&)>;

class Integrator {
public:
    /**
     * @brief Performs Runge-Kutta 4th order integration for rigid body dynamics.
     *
     * @param state Current state of the rigid body.
     * @param properties Properties of the rigid body (mass, inertia).
     * @param accumulators Accumulated forces and torques on the rigid body.
     * @param deltaTime Time step for integration.
     * @param derivativeFunction Function to calculate derivatives from state and forces.
     * @return New state after integration.
     */
    static RigidBodyState integrateRK4(
        const RigidBodyState& state,
        const RigidBodyProperties& properties,
        const RigidBodyForceAccumulators& accumulators,
        float deltaTime,
        StateDerivativeFunction derivativeFunction
    ) {
        // k1
        RigidBodyDerivative k1 = derivativeFunction(state, properties, accumulators);

        // k2
        RigidBodyState state_k2;
        state_k2.position = state.position + k1.dPosition * (deltaTime / 2.0f);
        state_k2.orientation = glm::normalize(state.orientation + k1.dOrientation * (deltaTime / 2.0f));
        state_k2.linearVelocity = state.linearVelocity + k1.dLinearVelocity * (deltaTime / 2.0f);
        state_k2.angularVelocity = state.angularVelocity + k1.dAngularVelocity * (deltaTime / 2.0f);
        RigidBodyDerivative k2 = derivativeFunction(state_k2, properties, accumulators);

        // k3
        RigidBodyState state_k3;
        state_k3.position = state.position + k2.dPosition * (deltaTime / 2.0f);
        state_k3.orientation = glm::normalize(state.orientation + k2.dOrientation * (deltaTime / 2.0f));
        state_k3.linearVelocity = state.linearVelocity + k2.dLinearVelocity * (deltaTime / 2.0f);
        state_k3.angularVelocity = state.angularVelocity + k2.dAngularVelocity * (deltaTime / 2.0f);
        RigidBodyDerivative k3 = derivativeFunction(state_k3, properties, accumulators);

        // k4
        RigidBodyState state_k4;
        state_k4.position = state.position + k3.dPosition * deltaTime;
        state_k4.orientation = glm::normalize(state.orientation + k3.dOrientation * deltaTime);
        state_k4.linearVelocity = state.linearVelocity + k3.dLinearVelocity * deltaTime;
        state_k4.angularVelocity = state.angularVelocity + k3.dAngularVelocity * deltaTime;
        RigidBodyDerivative k4 = derivativeFunction(state_k4, properties, accumulators);

        // Final state
        RigidBodyState newState;
        newState.position = state.position + (k1.dPosition + 2.0f * k2.dPosition + 2.0f * k3.dPosition + k4.dPosition) * (deltaTime / 6.0f);
        newState.orientation = glm::normalize(state.orientation + (k1.dOrientation + 2.0f * k2.dOrientation + 2.0f * k3.dOrientation + k4.dOrientation) * (deltaTime / 6.0f));
        newState.linearVelocity = state.linearVelocity + (k1.dLinearVelocity + 2.0f * k2.dLinearVelocity + 2.0f * k3.dLinearVelocity + k4.dLinearVelocity) * (deltaTime / 6.0f);
        newState.angularVelocity = state.angularVelocity + (k1.dAngularVelocity + 2.0f * k2.dAngularVelocity + 2.0f * k3.dAngularVelocity + k4.dAngularVelocity) * (deltaTime / 6.0f);

        return newState;
    }
};

} // namespace Physics

#endif // INTEGRATOR_H
