#!/usr/bin/env python3
"""
Simple test script for Claude Desktop MCP setup
Tests the basic functionality without complex dependencies
"""

import json
import socket
import sys
import os
from pathlib import Path

def test_blender_connection():
    """Test if Blender MCP server is running"""
    print("🔍 Testing Blender connection...")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex(('localhost', 9876))
        sock.close()
        
        if result == 0:
            print("✅ Blender MCP server is running on port 9876")
            return True
        else:
            print("❌ Cannot connect to Blender MCP server")
            print("💡 Make sure Blender is running with blender_mcp_server.py")
            return False
    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        return False

def test_blender_command():
    """Test sending a command to Blender"""
    print("🔍 Testing Blender command execution...")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        sock.connect(('localhost', 9876))
        
        # Send test command
        test_command = "print('Simple MCP Test Successful!')"
        message = json.dumps({"command": test_command}) + "\n"
        sock.send(message.encode('utf-8'))
        
        # Receive response
        response = sock.recv(1024).decode('utf-8')
        result = json.loads(response)
        
        sock.close()
        
        if result.get('status') == 'success':
            print("✅ Blender command execution successful")
            print(f"   Response: {result.get('result', 'No output')}")
            return True
        else:
            print(f"❌ Blender command failed: {result.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Command test failed: {e}")
        return False

def test_required_files():
    """Test if required files exist"""
    print("🔍 Testing required files...")
    
    project_root = Path(__file__).parent
    required_files = [
        "simple_claude_mcp.py",
        "blender_mcp_server.py",
        "claude_desktop_config.json",
        "SIMPLE_SETUP_GUIDE.md"
    ]
    
    all_exist = True
    for file_name in required_files:
        file_path = project_root / file_name
        if file_path.exists():
            print(f"   ✅ {file_name}")
        else:
            print(f"   ❌ {file_name} - Missing!")
            all_exist = False
    
    if all_exist:
        print("✅ All required files present")
    else:
        print("❌ Some required files are missing")
    
    return all_exist

def test_claude_config():
    """Test Claude Desktop configuration file"""
    print("🔍 Testing Claude Desktop config...")
    
    config_path = Path(__file__).parent / "claude_desktop_config.json"
    
    if not config_path.exists():
        print("❌ claude_desktop_config.json not found")
        return False
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        if "mcpServers" in config and "blender-control" in config["mcpServers"]:
            print("✅ Claude Desktop config is valid")
            server_config = config["mcpServers"]["blender-control"]
            print(f"   Command: {server_config.get('command', 'Not set')}")
            print(f"   Script: {server_config.get('args', ['Not set'])[0] if server_config.get('args') else 'Not set'}")
            return True
        else:
            print("❌ Claude Desktop config missing Blender MCP server")
            return False
            
    except Exception as e:
        print(f"❌ Error reading Claude config: {e}")
        return False

def test_python_execution():
    """Test if Python can execute the MCP script"""
    print("🔍 Testing Python MCP script...")
    
    script_path = Path(__file__).parent / "simple_claude_mcp.py"
    
    if not script_path.exists():
        print("❌ simple_claude_mcp.py not found")
        return False
    
    try:
        # Test if the script can be imported
        import subprocess
        result = subprocess.run([
            sys.executable, "-c", 
            f"import sys; sys.path.insert(0, '{script_path.parent}'); import simple_claude_mcp; print('Import successful')"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ Python MCP script can be imported")
            return True
        else:
            print(f"❌ Python MCP script import failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Python test failed: {e}")
        return False

def show_next_steps():
    """Show next steps for the user"""
    print("\n" + "=" * 60)
    print("📋 Next Steps:")
    print("=" * 60)
    print("\n1. 🎨 Open Blender:")
    print("   - Go to Scripting workspace")
    print("   - Open blender_mcp_server.py")
    print("   - Click Run Script")
    print("   - Look for: 'MCP Server started on localhost:9876'")
    
    print("\n2. ⚙️ Configure Claude Desktop:")
    print("   - Open Claude Desktop")
    print("   - Go to Settings > Developer")
    print("   - Click Edit Config")
    print("   - Copy content from claude_desktop_config.json")
    print("   - Save and restart Claude Desktop")
    
    print("\n3. 💬 Test in Claude Desktop:")
    print("   - Type: 'اتصل بـ Blender'")
    print("   - Type: 'حمّل سيارة Infernus'")
    print("   - Type: 'غيّر لون السيارة إلى أزرق'")
    
    print("\n📖 For detailed instructions, read:")
    print("   SIMPLE_SETUP_GUIDE.md")

def main():
    """Run all tests"""
    print("=" * 60)
    print("🧪 Simple Claude Desktop MCP Test Suite")
    print("=" * 60)
    
    tests = [
        ("Required Files", test_required_files),
        ("Claude Config", test_claude_config),
        ("Python Script", test_python_execution),
        ("Blender Connection", test_blender_connection),
        ("Blender Command", test_blender_command),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed >= 3:  # At least basic files and config should pass
        print("\n🎉 Basic setup is ready!")
        if passed < total:
            print("⚠️  Some tests failed, but you can still try the setup.")
            print("   The failed tests are likely due to Blender not running.")
        show_next_steps()
    else:
        print(f"\n⚠️  {total - passed} critical test(s) failed.")
        print("Please fix the issues above before proceeding.")
    
    return passed >= 3

if __name__ == "__main__":
    success = main()
    
    print(f"\n{'✅' if success else '❌'} Test completed.")
    
    if success:
        print("\n🚀 You're ready to try Claude Desktop with Blender!")
    else:
        print("\n🔧 Please fix the issues and run the test again.")
    
    sys.exit(0 if success else 1)
