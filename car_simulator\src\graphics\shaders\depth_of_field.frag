
#version 450 core
out vec4 FragColor;

in vec2 TexCoords;

uniform sampler2D screenTexture; // The rendered scene
uniform sampler2D depthTexture;  // The depth buffer
uniform float focusDistance;     // Distance to the focal plane
uniform float focusRange;        // Range around the focal plane that is in focus
uniform float blurStrength;      // Overall blur strength

vec3 getBlurColor(sampler2D tex, vec2 uv, float radius) {
    vec3 color = vec3(0.0);
    float count = 0.0;
    for (float x = -1.0; x <= 1.0; x += 1.0) {
        for (float y = -1.0; y <= 1.0; y += 1.0) {
            color += texture(tex, uv + vec2(x, y) * radius).rgb;
            count += 1.0;
        }
    }
    return color / count;
}

void main()
{
    float depth = texture(depthTexture, TexCoords).r;

    // Linearize depth (assuming perspective projection)
    // This formula depends on your projection matrix setup. Adjust as needed.
    // For example, if using a standard GLM perspective, you might need to pass near/far planes.
    // For simplicity, let's assume depth is already somewhat linear or we can work with non-linear.
    // A more robust solution would linearize depth using projection matrix parameters.

    // Calculate blur amount based on distance from focal plane
    float distance = depth; // Assuming depth is already a linear distance for simplicity
    float blurAmount = abs(distance - focusDistance);
    blurAmount = max(0.0, blurAmount - focusRange); // Only blur outside focus range
    blurAmount = min(1.0, blurAmount * blurStrength); // Scale by blur strength

    vec3 color = texture(screenTexture, TexCoords).rgb;

    if (blurAmount > 0.01) { // Apply blur only if significant blur is needed
        // Simple box blur for demonstration. A real DOF would use a more sophisticated blur (e.g., Gaussian).
        color = getBlurColor(screenTexture, TexCoords, blurAmount * 0.005); // Adjust blur radius
    }

    FragColor = vec4(color, 1.0);
}


