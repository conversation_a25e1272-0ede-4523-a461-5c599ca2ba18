#ifndef WEATHER_SYSTEM_H
#define WEATHER_SYSTEM_H

#include <glm/glm.hpp>
#include <string>
#include <vector>

namespace Game {

enum class WeatherType {
    CLEAR,
    RAINY,
    SNOWY
};

struct WeatherState {
    WeatherType type;
    float precipitationIntensity; // 0.0 to 1.0
    float temperature;            // Celsius
    float windSpeed;              // m/s
};

class WeatherSystem {
public:
    WeatherSystem();

    /**
     * @brief Updates the weather state and its effects on the environment.
     * @param deltaTime The time elapsed since the last update.
     */
    void update(float deltaTime);

    /**
     * @brief Sets the current weather type.
     * @param type The desired weather type.
     */
    void setWeatherType(WeatherType type);

    /**
     * @brief Gets the current weather state.
     * @return The current WeatherState.
     */
    const WeatherState& getCurrentWeatherState() const { return m_weatherState; }

    /**
     * @brief Calculates the tire grip multiplier based on current weather conditions.
     * @return A multiplier (0.0 to 1.0) affecting tire grip.
     */
    float getTireGripMultiplier() const;

private:
    WeatherState m_weatherState;
    float m_timeAccumulator; // For weather transitions or dynamic changes

    void updateRainyWeather(float deltaTime);
    void updateSnowyWeather(float deltaTime);
};

} // namespace Game

#endif // WEATHER_SYSTEM_H