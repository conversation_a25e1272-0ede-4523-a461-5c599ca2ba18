#!/usr/bin/env python3
"""
Test script for <PERSON> MCP integration
Tests all components before using with <PERSON>
"""

import asyncio
import json
import socket
import sys
import time
from pathlib import Path

def test_mcp_imports():
    """Test if MCP libraries are available"""
    print("🔍 Testing MCP imports...")
    
    try:
        import mcp
        from mcp.server import Server
        from mcp.server.stdio import stdio_server
        from mcp.types import CallToolRequest, CallToolResult, Tool, TextContent
        print("✅ All MCP imports successful")
        return True
    except ImportError as e:
        print(f"❌ MCP import failed: {e}")
        print("💡 Run: pip install mcp>=1.0.0")
        return False

def test_blender_connection():
    """Test connection to Blender MCP server"""
    print("🔍 Testing Blender connection...")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex(('localhost', 9876))
        sock.close()
        
        if result == 0:
            print("✅ Blender MCP server is running on port 9876")
            return True
        else:
            print("❌ Cannot connect to Blender MCP server")
            print("💡 Make sure Blender is running with blender_mcp_server.py")
            return False
    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        return False

def test_blender_command():
    """Test sending a command to Blender"""
    print("🔍 Testing Blender command execution...")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        sock.connect(('localhost', 9876))
        
        # Send test command
        test_command = "print('MCP Test Successful!')"
        message = json.dumps({"command": test_command}) + "\n"
        sock.send(message.encode('utf-8'))
        
        # Receive response
        response = sock.recv(1024).decode('utf-8')
        result = json.loads(response)
        
        sock.close()
        
        if result.get('status') == 'success':
            print("✅ Blender command execution successful")
            print(f"   Response: {result.get('result', 'No output')}")
            return True
        else:
            print(f"❌ Blender command failed: {result.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Command test failed: {e}")
        return False

def test_mcp_server_script():
    """Test if the MCP server script can be imported"""
    print("🔍 Testing MCP server script...")
    
    try:
        # Add current directory to path
        sys.path.insert(0, str(Path(__file__).parent))
        
        # Try to import the server module
        import claude_desktop_mcp_server
        print("✅ MCP server script imports successfully")
        return True
    except ImportError as e:
        print(f"❌ MCP server script import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ MCP server script error: {e}")
        return False

def test_claude_config():
    """Test Claude Desktop configuration"""
    print("🔍 Testing Claude Desktop configuration...")
    
    # Get config path
    if sys.platform == "win32":
        config_path = Path.home() / "AppData" / "Roaming" / "Claude" / "claude_desktop_config.json"
    elif sys.platform == "darwin":
        config_path = Path.home() / "Library" / "Application Support" / "Claude" / "claude_desktop_config.json"
    else:
        config_path = Path.home() / ".config" / "Claude" / "claude_desktop_config.json"
    
    if not config_path.exists():
        print("❌ Claude Desktop config file not found")
        print(f"   Expected at: {config_path}")
        print("💡 Run setup_claude_desktop_mcp.py first")
        return False
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        if "mcpServers" in config and "blender-control" in config["mcpServers"]:
            print("✅ Claude Desktop config contains Blender MCP server")
            server_config = config["mcpServers"]["blender-control"]
            print(f"   Command: {server_config.get('command', 'Not set')}")
            print(f"   Args: {server_config.get('args', 'Not set')}")
            return True
        else:
            print("❌ Claude Desktop config missing Blender MCP server")
            print("💡 Run setup_claude_desktop_mcp.py to fix this")
            return False
            
    except Exception as e:
        print(f"❌ Error reading Claude config: {e}")
        return False

def test_required_files():
    """Test if all required files exist"""
    print("🔍 Testing required files...")
    
    project_root = Path(__file__).parent
    required_files = [
        "claude_desktop_mcp_server.py",
        "blender_mcp_server.py",
        "blender_mcp_client.py",
        "CLAUDE_DESKTOP_MCP_SETUP.md"
    ]
    
    all_exist = True
    for file_name in required_files:
        file_path = project_root / file_name
        if file_path.exists():
            print(f"   ✅ {file_name}")
        else:
            print(f"   ❌ {file_name} - Missing!")
            all_exist = False
    
    if all_exist:
        print("✅ All required files present")
    else:
        print("❌ Some required files are missing")
    
    return all_exist

async def test_mcp_server_async():
    """Test the MCP server in async mode"""
    print("🔍 Testing MCP server async functionality...")
    
    try:
        # Import the server
        from claude_desktop_mcp_server import server, list_tools
        
        # Test list_tools
        tools_result = await list_tools()
        tool_count = len(tools_result.tools)
        
        print(f"✅ MCP server has {tool_count} tools available")
        for tool in tools_result.tools[:3]:  # Show first 3 tools
            print(f"   - {tool.name}: {tool.description}")
        
        if tool_count > 3:
            print(f"   ... and {tool_count - 3} more tools")
        
        return True
    except Exception as e:
        print(f"❌ MCP server async test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("🧪 Claude Desktop MCP Integration Test Suite")
    print("=" * 60)
    
    tests = [
        ("MCP Imports", test_mcp_imports),
        ("Required Files", test_required_files),
        ("MCP Server Script", test_mcp_server_script),
        ("Claude Config", test_claude_config),
        ("Blender Connection", test_blender_connection),
        ("Blender Command", test_blender_command),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Async test
    print(f"\n📋 MCP Server Async:")
    try:
        async_result = asyncio.run(test_mcp_server_async())
        results.append(("MCP Server Async", async_result))
    except Exception as e:
        print(f"❌ Async test failed: {e}")
        results.append(("MCP Server Async", False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Your MCP setup is ready for Claude Desktop!")
        print("\n📋 Next steps:")
        print("1. 🔄 Restart Claude Desktop")
        print("2. 🎨 Make sure Blender is running with MCP server")
        print("3. 💬 Start using Claude Desktop with Blender!")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please fix the issues above.")
        print("\n💡 Common solutions:")
        print("- Run: python setup_claude_desktop_mcp.py")
        print("- Make sure Blender is running with blender_mcp_server.py")
        print("- Restart Claude Desktop after config changes")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
