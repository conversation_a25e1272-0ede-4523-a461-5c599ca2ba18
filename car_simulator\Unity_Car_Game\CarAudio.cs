using UnityEngine;

[RequireComponent(typeof(AudioSource))]
public class CarAudio : MonoBehaviour
{
    [Header("ملفات الصوت")]
    public AudioClip engineIdleSound;        // صوت المحرك في الخمول
    public AudioClip engineRevSound;         // صوت المحرك عند التسارع
    public AudioClip brakeSound;             // صوت المكابح
    public AudioClip hornSound;              // صوت الكلاكس
    public AudioClip tireScreechSound;       // صوت احتكاك الإطارات
    
    [Header("إعدادات الصوت")]
    [Range(0f, 1f)]
    public float masterVolume = 1f;          // مستوى الصوت الرئيسي
    [Range(0.5f, 2f)]
    public float minPitch = 0.8f;            // أقل طبقة صوتية
    [Range(0.5f, 2f)]
    public float maxPitch = 1.5f;            // أعلى طبقة صوتية
    
    // مكونات الصوت
    private AudioSource engineAudioSource;
    private AudioSource effectsAudioSource;
    
    // مراجع السيارة
    private CarController carController;
    private Rigidbody carRigidbody;
    
    // متغيرات الصوت
    private float currentEngineVolume;
    private float currentEnginePitch;
    private bool isPlayingBrakeSound;
    private bool isPlayingTireScreech;
    
    void Start()
    {
        // إعداد مصادر الصوت
        SetupAudioSources();
        
        // الحصول على مراجع السيارة
        carController = GetComponent<CarController>();
        carRigidbody = GetComponent<Rigidbody>();
        
        // بدء تشغيل صوت المحرك
        if (engineIdleSound != null)
        {
            engineAudioSource.clip = engineIdleSound;
            engineAudioSource.loop = true;
            engineAudioSource.Play();
        }
        
        Debug.Log("🔊 تم تهيئة نظام الصوت للسيارة");
    }
    
    void SetupAudioSources()
    {
        // مصدر صوت المحرك
        engineAudioSource = GetComponent<AudioSource>();
        engineAudioSource.volume = masterVolume * 0.7f;
        engineAudioSource.pitch = 1f;
        
        // مصدر صوت التأثيرات
        GameObject effectsObject = new GameObject("EffectsAudioSource");
        effectsObject.transform.SetParent(transform);
        effectsAudioSource = effectsObject.AddComponent<AudioSource>();
        effectsAudioSource.volume = masterVolume;
        effectsAudioSource.pitch = 1f;
    }
    
    void Update()
    {
        if (carController == null || carRigidbody == null) return;
        
        // تحديث صوت المحرك
        UpdateEngineSound();
        
        // تحديث أصوات التأثيرات
        UpdateEffectSounds();
        
        // التحكم في الكلاكس
        HandleHornInput();
    }
    
    void UpdateEngineSound()
    {
        // الحصول على مدخلات المحرك
        float motorInput = Mathf.Abs(Input.GetAxis("Vertical"));
        float currentSpeed = carRigidbody.velocity.magnitude;
        
        // حساب طبقة الصوت بناءً على السرعة والمدخلات
        float targetPitch = Mathf.Lerp(minPitch, maxPitch, (motorInput * 0.7f) + (currentSpeed * 0.01f));
        currentEnginePitch = Mathf.Lerp(currentEnginePitch, targetPitch, Time.deltaTime * 3f);
        
        // حساب مستوى الصوت
        float targetVolume = Mathf.Lerp(0.3f, 1f, motorInput);
        currentEngineVolume = Mathf.Lerp(currentEngineVolume, targetVolume, Time.deltaTime * 5f);
        
        // تطبيق الإعدادات
        engineAudioSource.pitch = currentEnginePitch;
        engineAudioSource.volume = currentEngineVolume * masterVolume;
        
        // تغيير صوت المحرك حسب الحالة
        if (motorInput > 0.1f && engineRevSound != null)
        {
            if (engineAudioSource.clip != engineRevSound)
            {
                engineAudioSource.clip = engineRevSound;
                engineAudioSource.Play();
            }
        }
        else if (engineIdleSound != null)
        {
            if (engineAudioSource.clip != engineIdleSound)
            {
                engineAudioSource.clip = engineIdleSound;
                engineAudioSource.Play();
            }
        }
    }
    
    void UpdateEffectSounds()
    {
        // صوت المكابح
        bool isBraking = Input.GetKey(KeyCode.Space);
        float currentSpeed = carRigidbody.velocity.magnitude;
        
        if (isBraking && currentSpeed > 5f && !isPlayingBrakeSound && brakeSound != null)
        {
            PlayBrakeSound();
        }
        else if (!isBraking && isPlayingBrakeSound)
        {
            StopBrakeSound();
        }
        
        // صوت احتكاك الإطارات
        bool shouldPlayTireScreech = (isBraking && currentSpeed > 10f) || 
                                   (Mathf.Abs(Input.GetAxis("Horizontal")) > 0.8f && currentSpeed > 15f);
        
        if (shouldPlayTireScreech && !isPlayingTireScreech && tireScreechSound != null)
        {
            PlayTireScreechSound();
        }
        else if (!shouldPlayTireScreech && isPlayingTireScreech)
        {
            StopTireScreechSound();
        }
    }
    
    void HandleHornInput()
    {
        // تشغيل الكلاكس بالضغط على H
        if (Input.GetKeyDown(KeyCode.H) && hornSound != null)
        {
            PlayHornSound();
        }
    }
    
    void PlayBrakeSound()
    {
        if (effectsAudioSource != null && brakeSound != null)
        {
            effectsAudioSource.clip = brakeSound;
            effectsAudioSource.loop = true;
            effectsAudioSource.volume = masterVolume * 0.6f;
            effectsAudioSource.Play();
            isPlayingBrakeSound = true;
        }
    }
    
    void StopBrakeSound()
    {
        if (effectsAudioSource != null && isPlayingBrakeSound)
        {
            effectsAudioSource.Stop();
            isPlayingBrakeSound = false;
        }
    }
    
    void PlayTireScreechSound()
    {
        if (!isPlayingBrakeSound && effectsAudioSource != null && tireScreechSound != null)
        {
            effectsAudioSource.clip = tireScreechSound;
            effectsAudioSource.loop = true;
            effectsAudioSource.volume = masterVolume * 0.5f;
            effectsAudioSource.Play();
            isPlayingTireScreech = true;
        }
    }
    
    void StopTireScreechSound()
    {
        if (effectsAudioSource != null && isPlayingTireScreech && !isPlayingBrakeSound)
        {
            effectsAudioSource.Stop();
            isPlayingTireScreech = false;
        }
    }
    
    void PlayHornSound()
    {
        if (hornSound != null)
        {
            // إنشاء مصدر صوت مؤقت للكلاكس
            GameObject tempAudio = new GameObject("TempHornAudio");
            tempAudio.transform.position = transform.position;
            AudioSource tempSource = tempAudio.AddComponent<AudioSource>();
            
            tempSource.clip = hornSound;
            tempSource.volume = masterVolume;
            tempSource.pitch = 1f;
            tempSource.Play();
            
            // حذف المصدر المؤقت بعد انتهاء الصوت
            Destroy(tempAudio, hornSound.length);
        }
    }
    
    // دوال للتحكم في مستوى الصوت من الخارج
    public void SetMasterVolume(float volume)
    {
        masterVolume = Mathf.Clamp01(volume);
        
        if (engineAudioSource != null)
        {
            engineAudioSource.volume = currentEngineVolume * masterVolume;
        }
        
        if (effectsAudioSource != null)
        {
            effectsAudioSource.volume = masterVolume * 0.6f;
        }
    }
    
    public void MuteAllSounds(bool mute)
    {
        if (engineAudioSource != null)
        {
            engineAudioSource.mute = mute;
        }
        
        if (effectsAudioSource != null)
        {
            effectsAudioSource.mute = mute;
        }
    }
    
    // إيقاف جميع الأصوات عند تدمير الكائن
    void OnDestroy()
    {
        StopBrakeSound();
        StopTireScreechSound();
    }
    
    // عرض معلومات الصوت في المحرر
    void OnGUI()
    {
        if (Application.isPlaying)
        {
            GUIStyle style = new GUIStyle(GUI.skin.label);
            style.fontSize = 12;
            style.normal.textColor = Color.cyan;
            
            GUI.Label(new Rect(Screen.width - 200, Screen.height - 100, 180, 20), 
                     $"مستوى الصوت: {(masterVolume * 100):F0}%", style);
            GUI.Label(new Rect(Screen.width - 200, Screen.height - 80, 180, 20), 
                     $"طبقة المحرك: {currentEnginePitch:F2}", style);
            GUI.Label(new Rect(Screen.width - 200, Screen.height - 60, 180, 20), 
                     "اضغط H للكلاكس", style);
        }
    }
}
