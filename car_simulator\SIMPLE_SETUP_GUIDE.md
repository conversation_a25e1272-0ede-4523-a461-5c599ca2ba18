# 🚀 دليل التجربة المبسط - <PERSON> + Blender

## 📋 ما نحتاجه:
1. ✅ Blender مفتوح
2. ✅ <PERSON> Desktop مثبت
3. ✅ ملفات المشروع جاهزة

## 🎯 خطوات التجربة (5 دقائق):

### الخطوة 1: تشغيل Blender MCP Server
1. **افتح Blender**
2. **اذهب إلى**: Scripting workspace (أعلى الشاشة)
3. **افتح ملف**: `blender_mcp_server.py`
   - File > Open > اختر الملف من `C:\ASP\car_simulator\blender_mcp_server.py`
4. **اضغط**: ▶️ Run Script
5. **تأكد من الرسالة**: "MCP Server started on localhost:9876"

### الخطوة 2: تكوين Claude Desktop
1. **افتح <PERSON> Des<PERSON>**
2. **اذهب إلى**: Settings ⚙️ (أس<PERSON><PERSON> اليسار)
3. **اختر**: Developer
4. **اضغط**: Edit Config

**انسخ والصق هذا المحتوى:**
```json
{
  "mcpServers": {
    "blender-control": {
      "command": "python",
      "args": [
        "C:/ASP/car_simulator/simple_claude_mcp.py"
      ],
      "env": {
        "PYTHONPATH": "C:/ASP/car_simulator"
      }
    }
  }
}
```

5. **احفظ الملف** (Ctrl+S)

### الخطوة 3: إعادة تشغيل Claude Desktop
1. **أغلق Claude Desktop تماماً**
2. **افتح Claude Desktop مرة أخرى**

## ✅ اختبار التجربة:

### في Claude Desktop، اكتب:
```
"اتصل بـ Blender"
```

**النتيجة المتوقعة:**
- ✅ Successfully connected to Blender!

### جرب هذه الأوامر:
```
"حمّل سيارة Infernus"
"أعد إضاءة احترافية"
"غيّر لون السيارة إلى أزرق"
"أنشئ أنيميشن دوران"
"ارندر المشهد"
```

## 🔧 إذا لم تعمل:

### المشكلة: لا تظهر أدوات Blender
**الحل:**
1. تأكد من إعادة تشغيل Claude Desktop
2. تحقق من ملف التكوين
3. تأكد من المسار الصحيح للملفات

### المشكلة: "Cannot connect to Blender"
**الحل:**
1. تأكد من تشغيل Blender
2. تأكد من تشغيل MCP server في Blender
3. تحقق من الرسالة في Blender Console

### المشكلة: "Car file not found"
**الحل:**
1. تأكد من وجود ملف `infernus.obj` في:
   `C:\ASP\car_simulator\blender_export\infernus\infernus.obj`
2. إذا لم يكن موجوداً، شغّل: `python convert_infernus.py`

## 🎉 النتيجة المتوقعة:

بعد التجربة الناجحة:
- ✅ تحكم كامل في Blender من Claude Desktop
- ✅ تحميل وتعديل نماذج السيارات
- ✅ إنشاء أنيميشن ورندر
- ✅ تغيير الألوان والإضاءة
- ✅ بدون حدود رسائل!

## 💬 أمثلة على المحادثات:

### مثال 1:
```
أنت: "حمّل سيارة وغيّر لونها إلى أحمر"
Claude: سأقوم بتحميل السيارة وتغيير لونها...
[يستخدم الأدوات تلقائياً]
✅ تم تحميل السيارة وتغيير اللون إلى أحمر!
```

### مثال 2:
```
أنت: "أنشئ مشهد كامل مع إضاءة وأنيميشن"
Claude: سأقوم بإنشاء مشهد متكامل...
[يحمل السيارة، يعد الإضاءة، ينشئ الأنيميشن]
✅ المشهد جاهز! هل تريد رندره؟
```

---

**🚗✨ استمتع بالتحكم الكامل في Blender من Claude Desktop!**

**ملاحظة مهمة:** تأكد من تشغيل Blender مع MCP server قبل استخدام أي أمر.
