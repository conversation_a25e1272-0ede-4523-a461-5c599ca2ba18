#ifndef CAR_CONTROLLER_H
#define CAR_CONTROLLER_H

#include <glm/glm.hpp>

namespace Game {

struct CarControlInput {
    float steerInput;   // -1.0 (full left) to 1.0 (full right)
    float throttleInput; // 0.0 (no throttle) to 1.0 (full throttle)
    float brakeInput;    // 0.0 (no brake) to 1.0 (full brake)
    bool handbrakeInput; // True for handbrake engaged
};

class CarController {
public:
    CarController();

    /**
     * @brief Updates the car control inputs based on user input or AI.
     * @param input The raw input values.
     */
    void updateInput(const CarControlInput& input);

    /**
     * @brief Calculates the steering angle based on input and car speed.
     * @param currentSpeed Current linear speed of the car.
     * @return The calculated steering angle in radians.
     */
    float calculateSteeringAngle(float currentSpeed) const;

    /**
     * @brief Calculates the engine torque based on throttle input and engine RPM.
     * @param engineRPM Current engine RPM.
     * @return The calculated engine torque.
     */
    float calculateEngineTorque(float engineRPM) const;

    /**
     * @brief Calculates the braking force based on brake input.
     * @return The calculated braking force.
     */
    float calculateBrakingForce() const;

    /**
     * @brief Applies force feedback effects to the controller.
     * @param forceFeedbackMagnitude Magnitude of the force feedback (e.g., from tire forces).
     */
    void applyForceFeedback(float forceFeedbackMagnitude);

    const CarControlInput& getCurrentInput() const { return m_currentInput; }

private:
    CarControlInput m_currentInput;

    // Parameters for dynamic steering (e.g., speed sensitivity)
    float m_maxSteeringAngle; // Max steering angle in radians
    float m_steeringSensitivity; // How quickly steering responds

    // Parameters for engine torque calculation
    // This would typically be a torque curve lookup, but for now, a simple model.
    float m_maxEngineTorque;

    // Parameters for braking
    float m_maxBrakingForce;
};

} // namespace Game

#endif // CAR_CONTROLLER_H