#ifndef AUDIO_ENGINE_H
#define AUDIO_ENGINE_H

#include <string>
#include <glm/glm.hpp>

// Forward declarations for ECS components
namespace ECS {
    struct TransformComponent;
    struct AudioSourceComponent;
}

namespace Audio {

class AudioEngine {
public:
    AudioEngine();
    ~AudioEngine();

    /**
     * @brief Initializes the audio engine (FMOD/Wwise).
     * @return True if initialization is successful, false otherwise.
     */
    bool initialize();

    /**
     * @brief Updates the audio system (e.g., listener position, sound spatialization).
     * @param deltaTime Time elapsed since the last update.
     */
    void update(float deltaTime);

    /**
     * @brief Loads a sound file.
     * @param filePath Path to the sound file.
     * @return A pointer to the loaded sound (implementation-specific).
     */
    void* loadSound(const std::string& filePath);

    /**
     * @brief Plays a sound.
     * @param sound A pointer to the sound to play.
     * @param position Position of the sound source.
     * @param loop Whether the sound should loop.
     */
    void playSound(void* sound, const glm::vec3& position, bool loop = false);

    /**
     * @brief Sets the listener position and orientation.
     * @param position Listener position.
     * @param forward Listener forward vector.
     * @param up Listener up vector.
     * @param velocity Listener velocity (for Doppler effect).
     */
    void setListenerAttributes(
        const glm::vec3& position,
        const glm::vec3& forward,
        const glm::vec3& up,
        const glm::vec3& velocity
    );

    /**
     * @brief Simulates the Doppler effect for a sound source.
     * @param sourcePosition Position of the sound source.
     * @param sourceVelocity Velocity of the sound source.
     * @param listenerPosition Position of the listener.
     * @param listenerVelocity Velocity of the listener.
     * @return The calculated pitch shift due to Doppler effect.
     */
    float calculateDopplerPitchShift(
        const glm::vec3& sourcePosition,
        const glm::vec3& sourceVelocity,
        const glm::vec3& listenerPosition,
        const glm::vec3& listenerVelocity
    );

    // TODO: Integrate with ECS system
    // For now, assume we have direct access to components for audio.
    // In a full ECS, an AudioSystem would iterate over relevant components.

private:
    // Internal audio API (FMOD/Wwise) specific objects
    void* m_audioSystem; // Placeholder for FMOD::System* or Wwise::IAkSoundEngine*
    void* m_listener;    // Placeholder for FMOD::Channel* or Wwise::AkGameObjectID

    // For Doppler effect calculation
    float m_speedOfSound; // Speed of sound in m/s
};

} // namespace Audio

#nendif // AUDIO_ENGINE_H