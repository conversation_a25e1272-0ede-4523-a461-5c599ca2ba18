#!/usr/bin/env python3
"""
Setup script for <PERSON> MCP integration
Automatically configures <PERSON> to work with Blender
"""

import json
import os
import sys
import subprocess
from pathlib import Path
import shutil

def get_claude_config_path():
    """Get Claude Desktop config file path based on OS"""
    if sys.platform == "win32":
        return Path(os.environ.get("APPDATA", "")) / "<PERSON>" / "claude_desktop_config.json"
    elif sys.platform == "darwin":
        return Path.home() / "Library" / "Application Support" / "Claude" / "claude_desktop_config.json"
    else:  # Linux
        return Path.home() / ".config" / "Claude" / "claude_desktop_config.json"

def install_mcp_requirements():
    """Install required MCP packages"""
    print("🔧 Installing MCP requirements...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            "mcp>=1.0.0", "asyncio-mqtt", "pydantic>=2.0.0", "typing-extensions"
        ])
        print("✅ MCP requirements installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install MCP requirements: {e}")
        return False

def backup_existing_config(config_path):
    """Backup existing Claude Desktop config"""
    if config_path.exists():
        backup_path = config_path.with_suffix('.json.backup')
        shutil.copy2(config_path, backup_path)
        print(f"📋 Existing config backed up to: {backup_path}")
        return True
    return False

def create_claude_config():
    """Create or update Claude Desktop configuration"""
    config_path = get_claude_config_path()
    project_root = Path(__file__).parent.absolute()
    server_script = project_root / "claude_desktop_mcp_server.py"
    
    print(f"📁 Config path: {config_path}")
    print(f"📁 Server script: {server_script}")
    
    # Create config directory if it doesn't exist
    config_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Backup existing config
    backup_existing_config(config_path)
    
    # Load existing config or create new one
    config = {}
    if config_path.exists():
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            config = {}
    
    # Ensure mcpServers section exists
    if "mcpServers" not in config:
        config["mcpServers"] = {}
    
    # Add our Blender MCP server
    config["mcpServers"]["blender-control"] = {
        "command": "python",
        "args": [str(server_script)],
        "env": {
            "PYTHONPATH": str(project_root)
        }
    }
    
    # Write updated config
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        print("✅ Claude Desktop config updated successfully!")
        return True
    except Exception as e:
        print(f"❌ Failed to write config: {e}")
        return False

def check_blender_server():
    """Check if Blender MCP server script exists"""
    project_root = Path(__file__).parent
    blender_server = project_root / "blender_mcp_server.py"
    
    if blender_server.exists():
        print("✅ Blender MCP server script found")
        return True
    else:
        print("❌ Blender MCP server script not found!")
        print(f"   Expected at: {blender_server}")
        return False

def test_mcp_server():
    """Test if the MCP server can be imported"""
    try:
        import asyncio
        print("✅ asyncio available")
        
        # Try to import MCP
        import mcp
        print("✅ MCP library available")
        
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def main():
    """Main setup function"""
    print("=" * 60)
    print("🚀 Claude Desktop MCP Setup for Blender Control")
    print("=" * 60)
    
    # Step 1: Install requirements
    if not install_mcp_requirements():
        print("❌ Setup failed at requirements installation")
        return False
    
    # Step 2: Test MCP server
    if not test_mcp_server():
        print("❌ Setup failed at MCP server test")
        return False
    
    # Step 3: Check Blender server
    if not check_blender_server():
        print("❌ Setup failed at Blender server check")
        return False
    
    # Step 4: Create Claude config
    if not create_claude_config():
        print("❌ Setup failed at Claude config creation")
        return False
    
    # Success message
    print("\n" + "=" * 60)
    print("🎉 Setup completed successfully!")
    print("=" * 60)
    print("\n📋 Next steps:")
    print("1. 🔄 Restart Claude Desktop completely")
    print("2. 🎨 Open Blender and run 'blender_mcp_server.py'")
    print("3. 💬 Start chatting with Claude Desktop!")
    print("\n🎯 You can now control Blender from Claude Desktop without message limits!")
    print("\n📖 Read CLAUDE_DESKTOP_MCP_SETUP.md for detailed usage instructions")
    
    return True

if __name__ == "__main__":
    success = main()
    
    if not success:
        print("\n❌ Setup failed. Please check the errors above.")
        sys.exit(1)
    else:
        print("\n✅ Setup completed successfully!")
        
        # Ask if user wants to open the guide
        try:
            response = input("\n📖 Do you want to open the setup guide? (y/n): ").lower()
            if response in ['y', 'yes']:
                guide_path = Path(__file__).parent / "CLAUDE_DESKTOP_MCP_SETUP.md"
                if sys.platform == "win32":
                    os.startfile(guide_path)
                elif sys.platform == "darwin":
                    subprocess.run(["open", guide_path])
                else:
                    subprocess.run(["xdg-open", guide_path])
        except KeyboardInterrupt:
            print("\n👋 Setup complete. Goodbye!")
