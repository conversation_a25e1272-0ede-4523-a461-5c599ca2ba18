#pragma once

#include <glm/glm.hpp>
#include <cmath>

namespace Physics {

// Pacejka Magic Formula tire model parameters
struct TireParameters {
    // Longitudinal force parameters
    float B_long = 10.0f;       // Stiffness factor
    float C_long = 1.9f;        // Shape factor
    float D_long = 1.0f;        // Peak factor
    float E_long = 0.97f;       // Curvature factor
    float Sh_long = 0.0f;       // Horizontal shift
    float Sv_long = 0.0f;       // Vertical shift

    // Lateral force parameters
    float B_lat = 8.0f;         // Stiffness factor
    float C_lat = 1.3f;         // Shape factor
    float D_lat = 1.0f;         // Peak factor
    float E_lat = -0.5f;        // Curvature factor
    float Sh_lat = 0.0f;        // Horizontal shift
    float Sv_lat = 0.0f;        // Vertical shift

    // Aligning moment parameters
    float B_moment = 5.0f;      // Stiffness factor
    float C_moment = 2.0f;      // Shape factor
    float D_moment = 0.1f;      // Peak factor
    float E_moment = -1.0f;     // Curvature factor

    // Physical tire properties
    float radius = 0.3f;        // Tire radius (m)
    float width = 0.2f;         // Tire width (m)
    float aspectRatio = 0.65f;  // Aspect ratio (height/width)
    float rimDiameter = 0.4f;   // Rim diameter (m)

    // Load sensitivity
    float loadSensitivity = 1.0f;
    float pressureSensitivity = 1.0f;

    // Temperature effects
    float optimalTemperature = 80.0f;   // Optimal operating temperature (°C)
    float temperatureSensitivity = 0.01f;

    // Wear model
    float wearRate = 0.0001f;
    float currentWear = 0.0f;   // 0.0 = new, 1.0 = completely worn
};

// Tire state information
struct TireState {
    float normalLoad = 5000.0f;     // Normal force on tire (N)
    float slipRatio = 0.0f;         // Longitudinal slip ratio
    float slipAngle = 0.0f;         // Lateral slip angle (rad)
    float camberAngle = 0.0f;       // Camber angle (rad)
    float temperature = 20.0f;      // Tire temperature (°C)
    float pressure = 2.2f;          // Tire pressure (bar)

    glm::vec3 contactPatchPosition = glm::vec3(0.0f);
    glm::vec3 contactPatchVelocity = glm::vec3(0.0f);

    // Calculated forces
    glm::vec3 longitudinalForce = glm::vec3(0.0f);
    glm::vec3 lateralForce = glm::vec3(0.0f);
    glm::vec3 normalForce = glm::vec3(0.0f);
    glm::vec3 aligningMoment = glm::vec3(0.0f);
};

// Advanced tire model class
class TireModel {
public:
    TireModel(const TireParameters& parameters);
    ~TireModel();

    // Main force calculation
    void calculateForces(TireState& state) const;

    // Individual force components
    float calculateLongitudinalForce(float slipRatio, float normalLoad, float temperature = 20.0f) const;
    float calculateLateralForce(float slipAngle, float normalLoad, float camber = 0.0f, float temperature = 20.0f) const;
    float calculateAligningMoment(float slipAngle, float normalLoad, float temperature = 20.0f) const;

    // Combined slip calculations
    glm::vec2 calculateCombinedForces(float slipRatio, float slipAngle, float normalLoad) const;

    // Slip calculations
    float calculateSlipRatio(float wheelSpeed, float vehicleSpeed) const;
    float calculateSlipAngle(const glm::vec3& wheelVelocity, const glm::vec3& wheelDirection) const;

    // Load transfer effects
    float calculateLoadTransfer(float lateralAcceleration, float longitudinalAcceleration,
                               float centerOfMassHeight, float trackWidth) const;

    // Temperature model
    void updateTemperature(TireState& state, float deltaTime, float ambientTemperature = 20.0f) const;

    // Wear model
    void updateWear(TireState& state, float deltaTime) const;

    // Getters/Setters
    const TireParameters& getParameters() const { return m_parameters; }
    void setParameters(const TireParameters& parameters) { m_parameters = parameters; }

    // Utility functions
    float getContactPatchLength() const;
    float getContactPatchWidth() const;
    float getContactPatchArea() const;

    // Performance characteristics
    float getMaximumGrip(float normalLoad) const;
    float getOptimalSlipRatio() const;
    float getOptimalSlipAngle() const;

private:
    TireParameters m_parameters;

    // Internal calculation helpers
    float pacejkaFormula(float input, float B, float C, float D, float E, float Sh = 0.0f, float Sv = 0.0f) const;
    float temperatureMultiplier(float temperature) const;
    float wearMultiplier(float wear) const;
    float pressureMultiplier(float pressure) const;

    // Combined slip weighting
    float combinedSlipWeighting(float slipRatio, float slipAngle) const;
};

// Utility functions for tire calculations
namespace TireUtils {
    // Convert between different slip definitions
    float slipRatioToSlipPercentage(float slipRatio);
    float slipPercentageToSlipRatio(float slipPercentage);

    // Tire geometry calculations
    float calculateRollingRadius(float staticRadius, float normalLoad, float stiffness);
    float calculateContactPatchPressure(float normalLoad, float contactArea);

    // Performance metrics
    float calculateRollingResistance(float normalLoad, float speed, float temperature = 20.0f);
    float calculateHeatGeneration(float slipRatio, float slipAngle, float normalLoad, float speed);
}

} // namespace Physics