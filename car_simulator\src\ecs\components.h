#ifndef ECS_COMPONENTS_H
#define ECS_COMPONENTS_H

#include <glm/glm.hpp>
#include <glm/gtc/quaternion.hpp>
#include <glm/gtx/quaternion.hpp>

#include "../physics/PacejkaTireModel.h"
#include "../physics/RigidBody.h"

namespace ECS {

// Forward declarations
struct TransformComponent;
struct RigidBodyComponent;
struct RenderMeshComponent;
struct TireComponent;

// --- Component Definitions ---

struct TransformComponent {
    glm::vec3 position;
    glm::quat rotation;
    glm::vec3 scale;

    TransformComponent(
        const glm::vec3& pos = glm::vec3(0.0f),
        const glm::quat& rot = glm::quat(1.0f, 0.0f, 0.0f, 0.0f),
        const glm::vec3& scl = glm::vec3(1.0f))
        : position(pos), rotation(rot), scale(scl) {}

    glm::mat4 getModelMatrix() const {
        glm::mat4 model = glm::mat4(1.0f);
        model = glm::translate(model, position);
        model *= glm::mat4_cast(rotation);
        model = glm::scale(model, scale);
        return model;
    }
};

struct RigidBodyComponent {
    Physics::RigidBodyState state;
    Physics::RigidBodyProperties properties;
    Physics::RigidBodyForceAccumulators accumulators;

    RigidBodyComponent(
        float mass = 1.0f,
        const glm::mat3& inertia = glm::mat3(1.0f))
        : properties(mass, inertia) {
        accumulators.clear();
    }

    void applyForce(const glm::vec3& force) {
        accumulators.force += force;
    }

    void applyTorque(const glm::vec3& torque) {
        accumulators.torque += torque;
    }

    void clearAccumulators() {
        accumulators.clear();
    }
};

struct RenderMeshComponent {
    // Placeholder for Mesh and Material pointers
    void* mesh; // Replace with actual Mesh* type
    void* material; // Replace with actual Material* type

    RenderMeshComponent(void* m = nullptr, void* mat = nullptr)
        : mesh(m), material(mat) {}
};

struct TireComponent {
    Physics::PacejkaParameters params;
    float slipAngle; // Current slip angle
    float slipRatio; // Current slip ratio
    glm::vec3 tireForce; // Calculated force from tire model
    glm::vec3 tireTorque; // Calculated torque from tire model

    TireComponent(
        float B, float C, float D, float E, float Sh, float Sv)
        : slipAngle(0.0f), slipRatio(0.0f), tireForce(0.0f), tireTorque(0.0f) {
        params.B = B;
        params.C = C;
        params.D = D;
        params.E = E;
        params.Sh = Sh;
        params.Sv = Sv;
    }
};

} // namespace ECS

#endif // ECS_COMPONENTS_H